import React from 'react';
import dynamic from 'next/dynamic';
import { authPagesMenu } from '../menu';

const DefaultAside = dynamic(() => import('../pages/_layout/_asides/DefaultAside'));

const asides = [
	{ path: authPagesMenu.login.path, element: null, exact: true },
	{ path: authPagesMenu.signUp.path, element: null, exact: true },
	{ path: '/*', element: <DefaultAside />, exact: true },
];

export default asides;

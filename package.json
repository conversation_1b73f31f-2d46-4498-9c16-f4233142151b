{"name": "aadhan-dairy-erp", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3045", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint --fix .", "lint:scss": "stylelint **/*.scss", "lint:fix-scss": "stylelint --fix **/*.scss", "icon": "svgr SvgIcons -d components/icon/svg-icons --typescript"}, "dependencies": {"@hello-pangea/dnd": "^17.0.0", "@omtanke/react-use-event-outside": "^1.0.1", "@popperjs/core": "^2.11.8", "@reactour/tour": "^3.7.0", "animate.css": "^4.1.1", "apexcharts": "3.54.1", "bootstrap": "^5.3.3", "classnames": "^2.5.1", "dayjs": "^1.11.13", "formik": "^2.4.6", "framer-motion": "^11.11.9", "globals": "^15.11.0", "i18next": "^23.16.3", "jsx-to-string": "^1.4.0", "next": "^14.2.16", "next-i18next": "^15.3.1", "next-images": "^1.8.5", "pascalcase": "^2.0.0", "payment": "^2.4.7", "react": "18.3.1", "react-apexcharts": "^1.4.1", "react-big-calendar": "^1.15.0", "react-credit-cards-2": "2.0.0-rc1", "react-date-range": "^1.4.0", "react-dom": "18.3.1", "react-i18next": "^15.1.0", "react-input-mask": "^2.0.4", "react-jss": "^10.10.0", "react-notifications-component": "^4.0.1", "react-number-format": "^5.4.2", "react-popper": "^2.3.0", "react-scrollspy": "^3.4.3", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^10.0.6", "react-use": "^17.5.1", "sass": "^1.80.4", "use-clipboard-copy": "^0.2.0"}, "devDependencies": {"@eslint/compat": "^1.2.1", "@eslint/js": "^9.13.0", "@next/eslint-plugin-next": "^15.0.1", "@svgr/cli": "^8.1.0", "@types/node": "22.7.9", "@types/pascalcase": "^1.0.3", "@types/payment": "^2.1.7", "@types/prismjs": "^1.26.5", "@types/react": "18.3.12", "@types/react-big-calendar": "^1.8.12", "@types/react-date-range": "^1.4.9", "@types/react-dom": "18.3.1", "@types/react-input-mask": "^3.0.5", "@types/react-scrollspy": "^3.3.9", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-transition-group": "^4.4.11", "@typescript-eslint/eslint-plugin": "^8.11.0", "eslint": "9.13.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-next": "15.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "next-intercept-stdout": "^1.0.1", "prettier": "^3.3.3", "prismjs": "^1.29.0", "react-transition-group": "^4.4.5", "stylelint": "^16.10.0", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-twbs-bootstrap": "^15.1.0", "stylelint-declaration-block-no-ignored-properties": "^2.8.0", "stylelint-declaration-strict-value": "^1.10.6", "stylelint-no-px": "^2.0.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.2", "stylelint-scss": "^6.8.1", "typescript": "5.6.3", "typescript-eslint": "^8.11.0"}, "resolutions": {"json5": ">=2.2.2", "ua-parser-js": ">=0.7.33"}, "overrides": {"json5": ">=2.2.2", "ua-parser-js": ">=0.7.33"}}
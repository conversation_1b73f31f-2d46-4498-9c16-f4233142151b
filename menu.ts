// Dairy ERP Menu Configuration for Aadhan

export const dashboardPagesMenu = {
	dashboard: {
		id: 'dashboard',
		text: 'Dashboard',
		path: '/',
		icon: 'Dashboard',
		subMenu: null,
	},
};

// Dairy ERP Main Menu Configuration
export const dairyERPMenu = {
	procurement: {
		id: 'procurement',
		text: 'Procurement',
		path: 'procurement',
		icon: 'ShoppingCart',
		subMenu: {
			suppliers: {
				id: 'suppliers',
				text: 'Suppliers',
				path: 'procurement/suppliers',
				icon: 'Store',
			},
			purchaseOrders: {
				id: 'purchaseOrders',
				text: 'Purchase Orders',
				path: 'procurement/purchase-orders',
				icon: 'Receipt',
			},
			rawMaterials: {
				id: 'rawMaterials',
				text: 'Raw Materials',
				path: 'procurement/raw-materials',
				icon: 'Inventory',
			},
			procurement_dashboard: {
				id: 'procurement_dashboard',
				text: 'Procurement Dashboard',
				path: 'procurement/dashboard',
				icon: 'Dashboard',
			},
		},
	},
	salesMarketing: {
		id: 'salesMarketing',
		text: 'Sales & Marketing',
		path: 'sales-marketing',
		icon: 'TrendingUp',
		subMenu: {
			customerOrders: {
				id: 'customerOrders',
				text: 'Customer Orders',
				path: 'sales-marketing/orders',
				icon: 'ShoppingBag',
			},
			salesAnalytics: {
				id: 'salesAnalytics',
				text: 'Sales Analytics',
				path: 'sales-marketing/analytics',
				icon: 'Analytics',
			},
			marketingCampaigns: {
				id: 'marketingCampaigns',
				text: 'Marketing Campaigns',
				path: 'sales-marketing/campaigns',
				icon: 'Campaign',
			},
			distributors: {
				id: 'distributors',
				text: 'Distributors',
				path: 'sales-marketing/distributors',
				icon: 'AccountTree',
			},
		},
	},
	operation: {
		id: 'operation',
		text: 'Operation',
		path: 'operation',
		icon: 'Settings',
		subMenu: {
			production: {
				id: 'production',
				text: 'Production Planning',
				path: 'operation/production',
				icon: 'Factory',
			},
			qualityControl: {
				id: 'qualityControl',
				text: 'Quality Control',
				path: 'operation/quality-control',
				icon: 'VerifiedUser',
			},
			operationDashboard: {
				id: 'operationDashboard',
				text: 'Operation Dashboard',
				path: 'operation/dashboard',
				icon: 'Dashboard',
			},
			milkCollection: {
				id: 'milkCollection',
				text: 'Milk Collection',
				path: 'operation/milk-collection',
				icon: 'LocalDrink',
			},
		},
	},
	fssai: {
		id: 'fssai',
		text: 'FSSAI',
		path: 'fssai',
		icon: 'VerifiedUser',
		subMenu: {
			compliance: {
				id: 'compliance',
				text: 'Compliance Tracking',
				path: 'fssai/compliance',
				icon: 'CheckCircle',
			},
			documentation: {
				id: 'documentation',
				text: 'Documentation',
				path: 'fssai/documentation',
				icon: 'Description',
			},
			auditTrails: {
				id: 'auditTrails',
				text: 'Audit Trails',
				path: 'fssai/audit-trails',
				icon: 'History',
			},
			reports: {
				id: 'reports',
				text: 'Regulatory Reports',
				path: 'fssai/reports',
				icon: 'Assessment',
			},
		},
	},
	pis: {
		id: 'pis',
		text: 'PIS',
		path: 'pis',
		icon: 'Info',
		subMenu: {
			productCatalog: {
				id: 'productCatalog',
				text: 'Product Catalog',
				path: 'pis/catalog',
				icon: 'Category',
			},
			specifications: {
				id: 'specifications',
				text: 'Specifications',
				path: 'pis/specifications',
				icon: 'Assignment',
			},
			nutritionalInfo: {
				id: 'nutritionalInfo',
				text: 'Nutritional Information',
				path: 'pis/nutritional-info',
				icon: 'LocalDining',
			},
			lifecycle: {
				id: 'lifecycle',
				text: 'Product Lifecycle',
				path: 'pis/lifecycle',
				icon: 'Timeline',
			},
		},
	},
	inventory: {
		id: 'inventory',
		text: 'Inventory',
		path: 'inventory',
		icon: 'Inventory',
		subMenu: {
			stockManagement: {
				id: 'stockManagement',
				text: 'Stock Management',
				path: 'inventory/stock',
				icon: 'Storage',
			},
			warehouse: {
				id: 'warehouse',
				text: 'Warehouse Operations',
				path: 'inventory/warehouse',
				icon: 'Warehouse',
			},
			stockAlerts: {
				id: 'stockAlerts',
				text: 'Stock Alerts',
				path: 'inventory/alerts',
				icon: 'NotificationImportant',
			},
			inventoryReports: {
				id: 'inventoryReports',
				text: 'Inventory Reports',
				path: 'inventory/reports',
				icon: 'Assessment',
			},
		},
	},
};

// Continue Dairy ERP Menu Configuration
export const dairyERPMenuContinued = {
	crm: {
		id: 'crm',
		text: 'CRM',
		path: 'crm',
		icon: 'Contacts',
		subMenu: {
			customers: {
				id: 'customers',
				text: 'Customers',
				path: 'crm/customers',
				icon: 'PersonSearch',
			},
			distributorRelations: {
				id: 'distributorRelations',
				text: 'Distributor Relations',
				path: 'crm/distributors',
				icon: 'AccountTree',
			},
			customerAnalytics: {
				id: 'customerAnalytics',
				text: 'Customer Analytics',
				path: 'crm/analytics',
				icon: 'Analytics',
			},
			crmDashboard: {
				id: 'crmDashboard',
				text: 'CRM Dashboard',
				path: 'crm/dashboard',
				icon: 'Dashboard',
			},
		},
	},
	hrms: {
		id: 'hrms',
		text: 'HRMS',
		path: 'hrms',
		icon: 'People',
		subMenu: {
			employees: {
				id: 'employees',
				text: 'Employee Records',
				path: 'hrms/employees',
				icon: 'Person',
			},
			payroll: {
				id: 'payroll',
				text: 'Payroll',
				path: 'hrms/payroll',
				icon: 'Payment',
			},
			attendance: {
				id: 'attendance',
				text: 'Attendance',
				path: 'hrms/attendance',
				icon: 'Schedule',
			},
			performance: {
				id: 'performance',
				text: 'Performance Tracking',
				path: 'hrms/performance',
				icon: 'TrendingUp',
			},
		},
	},
	vehicle: {
		id: 'vehicle',
		text: 'Vehicle',
		path: 'vehicle',
		icon: 'LocalShipping',
		subMenu: {
			fleetManagement: {
				id: 'fleetManagement',
				text: 'Fleet Management',
				path: 'vehicle/fleet',
				icon: 'DirectionsCar',
			},
			deliveryTracking: {
				id: 'deliveryTracking',
				text: 'Delivery Tracking',
				path: 'vehicle/delivery',
				icon: 'LocalShipping',
			},
			maintenance: {
				id: 'maintenance',
				text: 'Vehicle Maintenance',
				path: 'vehicle/maintenance',
				icon: 'Build',
			},
			routeOptimization: {
				id: 'routeOptimization',
				text: 'Route Optimization',
				path: 'vehicle/routes',
				icon: 'Route',
			},
		},
	},
	asset: {
		id: 'asset',
		text: 'Asset',
		path: 'asset',
		icon: 'BusinessCenter',
		subMenu: {
			equipmentTracking: {
				id: 'equipmentTracking',
				text: 'Equipment Tracking',
				path: 'asset/equipment',
				icon: 'Precision Manufacturing',
			},
			maintenanceSchedules: {
				id: 'maintenanceSchedules',
				text: 'Maintenance Schedules',
				path: 'asset/maintenance',
				icon: 'Schedule',
			},
			depreciation: {
				id: 'depreciation',
				text: 'Asset Depreciation',
				path: 'asset/depreciation',
				icon: 'TrendingDown',
			},
			facilityManagement: {
				id: 'facilityManagement',
				text: 'Facility Management',
				path: 'asset/facility',
				icon: 'Business',
			},
		},
	},
};

// Final Dairy ERP Menu Modules
export const dairyERPMenuFinal = {
	canteen: {
		id: 'canteen',
		text: 'Canteen',
		path: 'canteen',
		icon: 'Restaurant',
		subMenu: {
			mealPlanning: {
				id: 'mealPlanning',
				text: 'Meal Planning',
				path: 'canteen/meal-planning',
				icon: 'MenuBook',
			},
			canteenInventory: {
				id: 'canteenInventory',
				text: 'Canteen Inventory',
				path: 'canteen/inventory',
				icon: 'Inventory',
			},
			billing: {
				id: 'billing',
				text: 'Billing',
				path: 'canteen/billing',
				icon: 'Receipt',
			},
			canteenOperations: {
				id: 'canteenOperations',
				text: 'Canteen Operations',
				path: 'canteen/operations',
				icon: 'Restaurant',
			},
		},
	},
	issueTracker: {
		id: 'issueTracker',
		text: 'Issue Tracker',
		path: 'issue-tracker',
		icon: 'BugReport',
		subMenu: {
			issueReporting: {
				id: 'issueReporting',
				text: 'Issue Reporting',
				path: 'issue-tracker/reporting',
				icon: 'Report',
			},
			ticketManagement: {
				id: 'ticketManagement',
				text: 'Ticket Management',
				path: 'issue-tracker/tickets',
				icon: 'ConfirmationNumber',
			},
			resolutionTracking: {
				id: 'resolutionTracking',
				text: 'Resolution Tracking',
				path: 'issue-tracker/resolution',
				icon: 'CheckCircle',
			},
			supportAnalytics: {
				id: 'supportAnalytics',
				text: 'Support Analytics',
				path: 'issue-tracker/analytics',
				icon: 'Analytics',
			},
		},
	},
	core: {
		id: 'core',
		text: 'Core',
		path: 'core',
		icon: 'Settings',
		subMenu: {
			systemSettings: {
				id: 'systemSettings',
				text: 'System Settings',
				path: 'core/settings',
				icon: 'Settings',
			},
			configuration: {
				id: 'configuration',
				text: 'Configuration',
				path: 'core/configuration',
				icon: 'Tune',
			},
			administration: {
				id: 'administration',
				text: 'Administration',
				path: 'core/administration',
				icon: 'AdminPanelSettings',
			},
			systemLogs: {
				id: 'systemLogs',
				text: 'System Logs',
				path: 'core/logs',
				icon: 'Description',
			},
		},
	},
	roles: {
		id: 'roles',
		text: 'Roles',
		path: 'roles',
		icon: 'Group',
		subMenu: {
			userRoles: {
				id: 'userRoles',
				text: 'User Roles',
				path: 'roles/user-roles',
				icon: 'Person',
			},
			permissions: {
				id: 'permissions',
				text: 'Permissions',
				path: 'roles/permissions',
				icon: 'Security',
			},
			accessControl: {
				id: 'accessControl',
				text: 'Access Control',
				path: 'roles/access-control',
				icon: 'Lock',
			},
			roleManagement: {
				id: 'roleManagement',
				text: 'Role Management',
				path: 'roles/management',
				icon: 'ManageAccounts',
			},
		},
	},
	security: {
		id: 'security',
		text: 'Security',
		path: 'security',
		icon: 'Security',
		subMenu: {
			securitySettings: {
				id: 'securitySettings',
				text: 'Security Settings',
				path: 'security/settings',
				icon: 'SecurityUpdate',
			},
			auditLogs: {
				id: 'auditLogs',
				text: 'Audit Logs',
				path: 'security/audit-logs',
				icon: 'History',
			},
			userActivity: {
				id: 'userActivity',
				text: 'User Activity',
				path: 'security/user-activity',
				icon: 'Visibility',
			},
			securityPolicies: {
				id: 'securityPolicies',
				text: 'Security Policies',
				path: 'security/policies',
				icon: 'Policy',
			},
		},
	},
};

// Authentication and System Pages
export const authPagesMenu = {
	login: {
		id: 'login',
		text: 'Login',
		path: 'auth-pages/login',
		icon: 'Login',
	},
	signUp: {
		id: 'signUp',
		text: 'Sign Up',
		path: 'auth-pages/sign-up',
		icon: 'PersonAdd',
	},
	page404: {
		id: 'Page404',
		text: '404 Page',
		path: '404',
		icon: 'ReportGmailerrorred',
	},
	logoff: {
		id: 'logoff',
		text: 'Logoff',
		path: 'auth-pages/logout',
		icon: 'Logout',
	},
};

// Combined Dairy ERP Menu for easy access
export const dairyERPMainMenu = {
	...dairyERPMenu,
	...dairyERPMenuContinued,
	...dairyERPMenuFinal,
};

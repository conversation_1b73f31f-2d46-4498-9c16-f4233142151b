import React, { useEffect, useRef, useState } from 'react';
import { useFormik } from 'formik';
import { useRouter } from 'next/router';
import Icon from './icon/Icon';
import Input from './bootstrap/forms/Input';
import Modal, { ModalBody, ModalHeader } from './bootstrap/Modal';
import { dairyERPMenu, dashboardPagesMenu } from '../menu';

const Search = () => {
	const refSearchInput = useRef<HTMLInputElement>(null);
	const router = useRouter();
	const [searchModalStatus, setSearchModalStatus] = useState(false);
	const formik = useFormik({
		initialValues: {
			searchInput: '',
		},
		onSubmit: () => {
			setSearchModalStatus(true);
		},
	});

	useEffect(() => {
		if (formik.values.searchInput) {
			setSearchModalStatus(true);
			refSearchInput?.current?.focus();
		}
		return () => {
			setSearchModalStatus(false);
		};
	}, [formik.values.searchInput]);

	const searchPages: {
		[key: string]: {
			id: string;
			text: string;
			path: string;
			icon: string;
		};
	} = {
		// Dashboard
		dashboard: {
			id: 'dashboard',
			text: 'Dashboard',
			path: '/',
			icon: 'Dashboard',
		},

		// Main ERP Modules
		procurement: {
			id: 'procurement',
			text: 'Procurement',
			path: '/procurement',
			icon: 'ShoppingCart',
		},
		suppliers: {
			id: 'suppliers',
			text: 'Suppliers',
			path: '/procurement/suppliers',
			icon: 'Store',
		},
		purchaseOrders: {
			id: 'purchaseOrders',
			text: 'Purchase Orders',
			path: '/procurement/purchase-orders',
			icon: 'Receipt',
		},
		rawMaterials: {
			id: 'rawMaterials',
			text: 'Raw Materials',
			path: '/procurement/raw-materials',
			icon: 'Inventory',
		},
		sales: {
			id: 'sales',
			text: 'Sales & Marketing',
			path: '/sales',
			icon: 'TrendingUp',
		},
		salesOrders: {
			id: 'salesOrders',
			text: 'Sales Orders',
			path: '/sales/orders',
			icon: 'ShoppingCart',
		},
		customers: {
			id: 'customers',
			text: 'Customers',
			path: '/sales/customers',
			icon: 'People',
		},
		operations: {
			id: 'operations',
			text: 'Operations',
			path: '/operations',
			icon: 'PrecisionManufacturing',
		},
		fssai: {
			id: 'fssai',
			text: 'FSSAI',
			path: '/fssai',
			icon: 'VerifiedUser',
		},
		pis: {
			id: 'pis',
			text: 'PIS',
			path: '/pis',
			icon: 'Inventory2',
		},
		inventory: {
			id: 'inventory',
			text: 'Inventory',
			path: '/inventory',
			icon: 'Warehouse',
		},
		crm: {
			id: 'crm',
			text: 'CRM',
			path: '/crm',
			icon: 'People',
		},
		hrms: {
			id: 'hrms',
			text: 'HRMS',
			path: '/hrms',
			icon: 'Group',
		},
		vehicle: {
			id: 'vehicle',
			text: 'Vehicle',
			path: '/vehicle',
			icon: 'LocalShipping',
		},
		asset: {
			id: 'asset',
			text: 'Asset',
			path: '/asset',
			icon: 'BusinessCenter',
		},
		canteen: {
			id: 'canteen',
			text: 'Canteen',
			path: '/canteen',
			icon: 'Restaurant',
		},
		issueTracker: {
			id: 'issueTracker',
			text: 'Issue Tracker',
			path: '/issue-tracker',
			icon: 'BugReport',
		},
		core: {
			id: 'core',
			text: 'Core',
			path: '/core',
			icon: 'Settings',
		},
		roles: {
			id: 'roles',
			text: 'Roles',
			path: '/roles',
			icon: 'AdminPanelSettings',
		},
		security: {
			id: 'security',
			text: 'Security',
			path: '/security',
			icon: 'Security',
		},
	};
	const filterResult = Object.keys(searchPages)
		.filter(
			(key) =>
				searchPages[key] &&
				searchPages[key].text &&
				(searchPages[key].text
					.toString()
					.toLowerCase()
					.includes(formik.values.searchInput.toLowerCase()) ||
				searchPages[key].path
					.toString()
					.toLowerCase()
					.includes(formik.values.searchInput.toLowerCase())),
		)
		.map((i) => searchPages[i]);
	return (
		<>
			<div className='d-flex' data-tour='search'>
				<label className='border-0 bg-transparent cursor-pointer' htmlFor='searchInput'>
					<Icon icon='Search' size='2x' color='primary' />
				</label>
				<Input
					id='searchInput'
					type='search'
					className='border-0 shadow-none bg-transparent'
					placeholder='Search...'
					onChange={formik.handleChange}
					value={formik.values.searchInput}
					autoComplete='off'
				/>
			</div>
			<Modal
				setIsOpen={setSearchModalStatus}
				isOpen={searchModalStatus}
				isStaticBackdrop
				isScrollable
				data-tour='search-modal'>
				<ModalHeader setIsOpen={setSearchModalStatus}>
					<label className='border-0 bg-transparent cursor-pointer' htmlFor='searchInput'>
						<Icon icon='Search' size='2x' color='primary' />
					</label>
					<Input
						ref={refSearchInput}
						name='searchInput'
						className='border-0 shadow-none bg-transparent'
						placeholder='Search...'
						onChange={formik.handleChange}
						value={formik.values.searchInput}
					/>
				</ModalHeader>
				<ModalBody>
					<table className='table table-hover table-modern caption-top mb-0'>
						<caption>Results: {filterResult.length}</caption>
						<thead className='position-sticky' style={{ top: -13 }}>
							<tr>
								<th scope='col'>Pages</th>
							</tr>
						</thead>
						<tbody>
							{filterResult.length ? (
								filterResult.map((item) => (
									<tr
										key={item.id}
										className='cursor-pointer'
										onClick={() => {
											router.push(`/${item.path}`);
										}}>
										<td>
											{item.icon && (
												<Icon
													icon={item.icon}
													size='lg'
													className='me-2'
													color='primary'
												/>
											)}
											{item.text}
										</td>
									</tr>
								))
							) : (
								<tr className='table-active'>
									<td>No result found for query "{formik.values.searchInput}"</td>
								</tr>
							)}
						</tbody>
					</table>
				</ModalBody>
			</Modal>
		</>
	);
};

export default Search;

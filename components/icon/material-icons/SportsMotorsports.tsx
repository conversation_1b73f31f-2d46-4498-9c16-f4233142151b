import React, { SVGProps } from 'react';

const SvgSportsMotorsports = (props: SVGProps<SVGSVGElement>) => {
	return (
		<svg viewBox='0 0 24 24' fill='currentColor' className='svg-icon' {...props}>
			<path fill='none' d='M0 0h24v24H0z' />
			<path
				d='M13.56 6c-.15 0-.29 0-.44.01-1.45.1-2.72.43-3.82.99l2.5 1.06c1.33.57 2.2 1.87 2.2 3.32 0 1.99-1.62 3.61-3.61 3.61H4.24C4.01 16.28 4 17.19 4 17.2v.8h10c1.68 0 3.3-.71 4.44-1.96a5.99 5.99 0 001.54-4.63C19.69 8.37 16.87 6 13.56 6z'
				opacity={0.3}
			/>
			<path d='M21.96 11.22C21.57 7.01 17.76 4 13.56 4c-.19 0-.38.01-.57.02C2 4.74 2 17.2 2 17.2v.8c0 1.1.9 2 2 2h10c4.67 0 8.41-4.01 7.96-8.78zm-16.7.34c.57-1.29 1.28-2.35 2.14-3.19l3.62 1.53c.6.25.98.83.98 1.48 0 .89-.72 1.61-1.61 1.61H4.72c.15-.46.32-.94.54-1.43zm13.18 4.48A6.022 6.022 0 0114 18H4v-.8c0-.02.01-.92.24-2.2h6.15c1.99 0 3.61-1.62 3.61-3.61 0-1.45-.87-2.76-2.2-3.32L9.3 7.01c1.1-.57 2.37-.9 3.82-.99.15-.02.3-.02.44-.02 3.31 0 6.13 2.37 6.41 5.41.16 1.72-.38 3.36-1.53 4.63z' />
		</svg>
	);
};

export default SvgSportsMotorsports;

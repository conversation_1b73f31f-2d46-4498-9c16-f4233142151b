import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const CanteenDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample canteen data
	const todaysMenu = [
		{
			id: 'MENU-001',
			category: 'Breakfast',
			items: ['Poha', 'Upma', 'Tea', 'Coffee'],
			price: 25,
			available: 45,
			sold: 38,
			time: '8:00 AM - 10:00 AM',
		},
		{
			id: 'MENU-002',
			category: 'Lunch',
			items: ['Dal Rice', 'Roti', 'Sabzi', 'Salad'],
			price: 60,
			available: 80,
			sold: 72,
			time: '12:00 PM - 2:00 PM',
		},
		{
			id: 'MENU-003',
			category: 'Snacks',
			items: ['Samosa', 'Sandwich', 'Tea', 'Biscuits'],
			price: 20,
			available: 50,
			sold: 35,
			time: '4:00 PM - 6:00 PM',
		},
		{
			id: 'MENU-004',
			category: 'Dinner',
			items: ['Chapati', 'Dal', 'Rice', 'Curry'],
			price: 55,
			available: 40,
			sold: 28,
			time: '7:00 PM - 9:00 PM',
		},
	];

	const recentOrders = [
		{
			id: 'ORD-001',
			employee: 'Rajesh Kumar',
			empId: 'EMP-001',
			items: ['Lunch Combo', 'Tea'],
			amount: 65,
			time: '12:30 PM',
			status: 'Completed',
		},
		{
			id: 'ORD-002',
			employee: 'Priya Sharma',
			empId: 'EMP-002',
			items: ['Breakfast Combo'],
			amount: 25,
			time: '9:15 AM',
			status: 'Completed',
		},
		{
			id: 'ORD-003',
			employee: 'Amit Singh',
			empId: 'EMP-003',
			items: ['Snacks', 'Coffee'],
			amount: 35,
			time: '4:45 PM',
			status: 'Preparing',
		},
	];

	const getCategoryColor = (category: string) => {
		switch (category) {
			case 'Breakfast': return 'warning';
			case 'Lunch': return 'success';
			case 'Snacks': return 'info';
			case 'Dinner': return 'primary';
			default: return 'secondary';
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Completed': return 'success';
			case 'Preparing': return 'warning';
			case 'Pending': return 'info';
			case 'Cancelled': return 'danger';
			default: return 'secondary';
		}
	};

	const getAvailabilityColor = (available: number, sold: number) => {
		const percentage = (sold / available) * 100;
		if (percentage >= 90) return 'danger';
		if (percentage >= 70) return 'warning';
		return 'success';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Canteen Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Restaurant' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Canteen Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Menu Item
					</Button>
					<Button
						icon='Receipt'
						color='success'
						isLight>
						Generate Bill
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Canteen Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Restaurant' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>173</div>
										<div className='text-muted'>Orders Today</div>
										<div className='small text-info'>
											<Icon icon='TrendingUp' className='me-1' />
											+15 from yesterday
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹8,450</div>
										<div className='text-muted'>Revenue Today</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Daily target met
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>142</div>
										<div className='text-muted'>Employees Served</div>
										<div className='small text-warning'>
											<Icon icon='Restaurant' className='me-1' />
											85% participation
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Star' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4.6</div>
										<div className='text-muted'>Food Rating</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Excellent feedback
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Today's Menu and Recent Orders */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Restaurant' /> Today's Menu
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Category</th>
												<th>Items</th>
												<th>Price</th>
												<th>Availability</th>
												<th>Time</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{todaysMenu.map((menu) => (
												<tr key={menu.id}>
													<td>
														<Badge color={getCategoryColor(menu.category)} isLight>
															{menu.category}
														</Badge>
													</td>
													<td>
														<div>
															{menu.items.map((item, index) => (
																<span key={index} className='badge bg-light text-dark me-1 mb-1'>
																	{item}
																</span>
															))}
														</div>
													</td>
													<td>
														<div className='fw-bold'>₹{menu.price}</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={(menu.sold / menu.available) * 100}
																	color={getAvailabilityColor(menu.available, menu.sold)}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{menu.sold}/{menu.available}
															</small>
														</div>
														<div className='text-muted small'>
															{menu.available - menu.sold} remaining
														</div>
													</td>
													<td>
														<div className='text-muted small'>{menu.time}</div>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Edit'
															className='me-2'>
															Edit
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Add'>
															Order
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Orders</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									{recentOrders.map((order) => (
										<div key={order.id} className='timeline-item'>
											<div className={`timeline-marker bg-${getStatusColor(order.status)}`}></div>
											<div className='timeline-content'>
												<h6 className='timeline-title'>{order.employee}</h6>
												<p className='timeline-text'>
													{order.items.join(', ')} - ₹{order.amount}
												</p>
												<div className='d-flex justify-content-between align-items-center'>
													<small className='text-muted'>{order.time}</small>
													<Badge color={getStatusColor(order.status)} isLight>
														{order.status}
													</Badge>
												</div>
											</div>
										</div>
									))}
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Inventory Status */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Canteen Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/canteen/menu'>
											<Icon icon='Restaurant' className='me-2' />
											Menu
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/canteen/orders'>
											<Icon icon='Receipt' className='me-2' />
											Orders
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/canteen/inventory'>
											<Icon icon='Inventory' className='me-2' />
											Inventory
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/canteen/billing'>
											<Icon icon='Payment' className='me-2' />
											Billing
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/canteen/feedback'>
											<Icon icon='Star' className='me-2' />
											Feedback
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/canteen/reports'>
											<Icon icon='Assessment' className='me-2' />
											Reports
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Inventory Status</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<div className='d-flex align-items-center p-3 bg-l10-success rounded'>
											<Icon icon='LocalDining' size='2x' color='success' className='me-3' />
											<div>
												<div className='fw-bold'>Rice</div>
												<div className='text-success small'>85% stock</div>
											</div>
										</div>
									</div>
									<div className='col-6'>
										<div className='d-flex align-items-center p-3 bg-l10-warning rounded'>
											<Icon icon='LocalDining' size='2x' color='warning' className='me-3' />
											<div>
												<div className='fw-bold'>Dal</div>
												<div className='text-warning small'>25% stock</div>
											</div>
										</div>
									</div>
									<div className='col-6'>
										<div className='d-flex align-items-center p-3 bg-l10-info rounded'>
											<Icon icon='LocalDining' size='2x' color='info' className='me-3' />
											<div>
												<div className='fw-bold'>Vegetables</div>
												<div className='text-info small'>60% stock</div>
											</div>
										</div>
									</div>
									<div className='col-6'>
										<div className='d-flex align-items-center p-3 bg-l10-danger rounded'>
											<Icon icon='LocalDining' size='2x' color='danger' className='me-3' />
											<div>
												<div className='fw-bold'>Oil</div>
												<div className='text-danger small'>15% stock</div>
											</div>
										</div>
									</div>
								</div>
								<div className='mt-3'>
									<Button
										color='warning'
										isLight
										className='w-100'
										icon='ShoppingCart'>
										Reorder Low Stock Items
									</Button>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default CanteenDashboard;

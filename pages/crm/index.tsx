import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';
import Chart, { IChartOptions } from '../../components/extras/Chart';

const CRMDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample CRM data
	const leads = [
		{
			id: 'LEAD-001',
			name: 'Metro Supermarket Chain',
			contact: 'Amit Kumar',
			phone: '+91 98765 43210',
			email: '<EMAIL>',
			source: 'Website',
			status: 'Qualified',
			value: 500000,
			probability: 75,
			lastContact: '2024-01-15',
		},
		{
			id: 'LEAD-002',
			name: 'Fresh Foods Distributor',
			contact: 'Priya Singh',
			phone: '+91 87654 32109',
			email: '<EMAIL>',
			source: 'Referral',
			status: 'Proposal',
			value: 300000,
			probability: 60,
			lastContact: '2024-01-14',
		},
		{
			id: 'LEAD-003',
			name: 'City Mall Food Court',
			contact: 'Raj Patel',
			phone: '+91 76543 21098',
			email: '<EMAIL>',
			source: 'Cold Call',
			status: 'Negotiation',
			value: 150000,
			probability: 40,
			lastContact: '2024-01-13',
		},
	];

	// Chart data for sales pipeline
	const pipelineData: IChartOptions = {
		series: [
			{
				name: 'Pipeline Value',
				data: [120000, 280000, 450000, 320000, 180000],
			},
		],
		options: {
			chart: {
				type: 'bar',
				height: 300,
			},
			xaxis: {
				categories: ['Lead', 'Qualified', 'Proposal', 'Negotiation', 'Closed'],
			},
			yaxis: {
				labels: {
					formatter: (value) => `₹${(value / 1000)}K`,
				},
			},
			colors: ['#3b82f6'],
		},
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Lead': return 'info';
			case 'Qualified': return 'primary';
			case 'Proposal': return 'warning';
			case 'Negotiation': return 'success';
			case 'Closed': return 'dark';
			default: return 'secondary';
		}
	};

	const getSourceColor = (source: string) => {
		switch (source) {
			case 'Website': return 'primary';
			case 'Referral': return 'success';
			case 'Cold Call': return 'warning';
			case 'Social Media': return 'info';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Customer Relationship Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Groups' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Customer Relationship Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Lead
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						CRM Report
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* CRM Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Groups' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>156</div>
										<div className='text-muted'>Total Leads</div>
										<div className='small text-info'>
											<Icon icon='TrendingUp' className='me-1' />
											+12 this week
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>42</div>
										<div className='text-muted'>Qualified Leads</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											27% conversion
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹13.5L</div>
										<div className='text-muted'>Pipeline Value</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											Expected this quarter
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Star' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4.2</div>
										<div className='text-muted'>Customer Rating</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Above industry avg
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Leads and Pipeline */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Groups' /> Active Leads
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Lead</th>
												<th>Contact</th>
												<th>Source</th>
												<th>Status</th>
												<th>Value</th>
												<th>Probability</th>
												<th>Last Contact</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{leads.map((lead) => (
												<tr key={lead.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Business' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{lead.name}</div>
																<div className='text-muted small'>{lead.id}</div>
															</div>
														</div>
													</td>
													<td>
														<div>
															<div className='fw-bold'>{lead.contact}</div>
															<div className='text-muted small'>{lead.phone}</div>
															<div className='text-muted small'>{lead.email}</div>
														</div>
													</td>
													<td>
														<Badge color={getSourceColor(lead.source)} isLight>
															{lead.source}
														</Badge>
													</td>
													<td>
														<Badge color={getStatusColor(lead.status)} isLight>
															{lead.status}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>₹{lead.value.toLocaleString()}</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={lead.probability}
																	color={lead.probability >= 70 ? 'success' : lead.probability >= 40 ? 'warning' : 'danger'}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{lead.probability}%
															</small>
														</div>
													</td>
													<td>{new Date(lead.lastContact).toLocaleDateString()}</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Phone'
															className='me-2'>
															Call
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Sales Pipeline</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={pipelineData.series}
									options={pipelineData.options}
									type='bar'
									height={300}
								/>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>CRM Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm/leads'>
											<Icon icon='Groups' className='me-2' />
											Leads
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm/customers'>
											<Icon icon='People' className='me-2' />
											Customers
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm/opportunities'>
											<Icon icon='TrendingUp' className='me-2' />
											Opportunities
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm/activities'>
											<Icon icon='Event' className='me-2' />
											Activities
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm/campaigns'>
											<Icon icon='Campaign' className='me-2' />
											Campaigns
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm/reports'>
											<Icon icon='Assessment' className='me-2' />
											Reports
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent CRM Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Lead qualified</h6>
											<p className='timeline-text'>Metro Supermarket Chain moved to qualified stage</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Follow-up call scheduled</h6>
											<p className='timeline-text'>Call with Fresh Foods Distributor tomorrow</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Proposal sent</h6>
											<p className='timeline-text'>Pricing proposal sent to City Mall Food Court</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New lead added</h6>
											<p className='timeline-text'>Restaurant chain inquiry from website</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default CRMDashboard;

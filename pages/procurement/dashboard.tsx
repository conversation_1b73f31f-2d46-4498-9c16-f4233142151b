import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';
import Chart, { IChartOptions } from '../../components/extras/Chart';

const ProcurementDashboardPage: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Chart data for procurement analytics
	const monthlySpendData: IChartOptions = {
		series: [
			{
				name: 'Monthly Spend',
				data: [180000, 220000, 195000, 240000, 210000, 260000],
			},
		],
		options: {
			chart: {
				type: 'line',
				height: 350,
			},
			xaxis: {
				categories: ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
			},
			yaxis: {
				labels: {
					formatter: (value) => `₹${(value / 1000).toFixed(0)}K`,
				},
			},
			stroke: {
				curve: 'smooth',
			},
			colors: ['#3b82f6'],
		},
	};

	const supplierPerformanceData: IChartOptions = {
		series: [85, 92, 78, 88],
		options: {
			chart: {
				type: 'donut',
				height: 300,
			},
			labels: ['ABC Dairy Supplies', 'Fresh Milk Producers', 'Quality Feed Solutions', 'Equipment Suppliers'],
			colors: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444'],
			legend: {
				position: 'bottom',
			},
		},
	};

	// Sample data
	const recentOrders = [
		{ id: 'PO-2024-005', supplier: 'ABC Dairy Supplies', amount: 35000, status: 'Pending' },
		{ id: 'PO-2024-006', supplier: 'Fresh Milk Producers', amount: 52000, status: 'Approved' },
		{ id: 'PO-2024-007', supplier: 'Quality Feed Solutions', amount: 28000, status: 'Delivered' },
	];

	const topSuppliers = [
		{ name: 'Fresh Milk Producers', orders: 24, amount: 580000, rating: 4.8 },
		{ name: 'ABC Dairy Supplies', orders: 18, amount: 420000, rating: 4.5 },
		{ name: 'Quality Feed Solutions', orders: 15, amount: 350000, rating: 4.2 },
		{ name: 'Equipment Suppliers Ltd', orders: 8, amount: 280000, rating: 4.0 },
	];

	return (
		<PageWrapper>
			<Head>
				<title>Procurement Dashboard - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Analytics' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Procurement Analytics Dashboard</span>
				</SubHeaderLeft>
			</SubHeader>
			<Page>
				{/* KPI Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹2.6L</div>
										<div className='text-muted'>Monthly Spend</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+12% from last month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Store' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>24</div>
										<div className='text-muted'>Active Suppliers</div>
										<div className='small text-info'>
											<Icon icon='Add' className='me-1' />
											2 new this month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Receipt' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>15</div>
										<div className='text-muted'>Pending Orders</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											3 overdue
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Savings' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹45K</div>
										<div className='text-muted'>Cost Savings</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											8% this quarter
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Charts Row */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Monthly Procurement Spend Trend</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={monthlySpendData.series}
									options={monthlySpendData.options}
									type='line'
									height={350}
								/>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Supplier Performance</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={supplierPerformanceData.series}
									options={supplierPerformanceData.options}
									type='donut'
									height={300}
								/>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Data Tables Row */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Purchase Orders</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern'>
										<thead>
											<tr>
												<th>PO Number</th>
												<th>Supplier</th>
												<th>Amount</th>
												<th>Status</th>
											</tr>
										</thead>
										<tbody>
											{recentOrders.map((order) => (
												<tr key={order.id}>
													<td className='fw-bold text-primary'>{order.id}</td>
													<td>{order.supplier}</td>
													<td>₹{order.amount.toLocaleString()}</td>
													<td>
														<Badge
															color={
																order.status === 'Pending' ? 'warning' :
																order.status === 'Approved' ? 'info' :
																'success'
															}
															isLight>
															{order.status}
														</Badge>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Top Suppliers by Volume</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern'>
										<thead>
											<tr>
												<th>Supplier</th>
												<th>Orders</th>
												<th>Amount</th>
												<th>Rating</th>
											</tr>
										</thead>
										<tbody>
											{topSuppliers.map((supplier, index) => (
												<tr key={index}>
													<td>
														<div className='fw-bold'>{supplier.name}</div>
													</td>
													<td>{supplier.orders}</td>
													<td>₹{(supplier.amount / 1000).toFixed(0)}K</td>
													<td>
														<div className='d-flex align-items-center'>
															<Icon icon='Star' color='warning' className='me-1' />
															{supplier.rating}
														</div>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default ProcurementDashboardPage;

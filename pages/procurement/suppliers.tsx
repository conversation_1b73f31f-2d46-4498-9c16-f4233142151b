import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Modal, { ModalBody, ModalFooter, ModalHeader, ModalTitle } from '../../components/bootstrap/Modal';
import FormGroup from '../../components/bootstrap/forms/FormGroup';
import Input from '../../components/bootstrap/forms/Input';
import Label from '../../components/bootstrap/forms/Label';

const SuppliersPage: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);
	const [isModalOpen, setIsModalOpen] = useState(false);

	// Sample supplier data
	const suppliers = [
		{
			id: 1,
			name: 'ABC Dairy Supplies',
			contact: 'John Doe',
			phone: '+91 98765 43210',
			email: '<EMAIL>',
			category: 'Packaging',
			status: 'Active',
			rating: 4.5,
		},
		{
			id: 2,
			name: 'Fresh Milk Producers',
			contact: 'Jane Smith',
			phone: '+91 87654 32109',
			email: '<EMAIL>',
			category: 'Raw Milk',
			status: 'Active',
			rating: 4.8,
		},
		{
			id: 3,
			name: 'Quality Feed Solutions',
			contact: 'Mike Johnson',
			phone: '+91 76543 21098',
			email: '<EMAIL>',
			category: 'Animal Feed',
			status: 'Inactive',
			rating: 4.2,
		},
	];

	return (
		<PageWrapper>
			<Head>
				<title>Suppliers - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Store' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Suppliers Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						onClick={() => setIsModalOpen(true)}>
						Add New Supplier
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Store' /> Supplier Directory
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Supplier Name</th>
												<th>Contact Person</th>
												<th>Phone</th>
												<th>Category</th>
												<th>Rating</th>
												<th>Status</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{suppliers.map((supplier) => (
												<tr key={supplier.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Store' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{supplier.name}</div>
																<div className='text-muted small'>{supplier.email}</div>
															</div>
														</div>
													</td>
													<td>{supplier.contact}</td>
													<td>{supplier.phone}</td>
													<td>
														<Badge
															color={
																supplier.category === 'Raw Milk' ? 'primary' :
																supplier.category === 'Packaging' ? 'success' :
																'warning'
															}
															isLight>
															{supplier.category}
														</Badge>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<Icon icon='Star' color='warning' className='me-1' />
															{supplier.rating}
														</div>
													</td>
													<td>
														<Badge
															color={supplier.status === 'Active' ? 'success' : 'danger'}
															isLight>
															{supplier.status}
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Edit'
															className='me-2'>
															Edit
														</Button>
														<Button
															color='danger'
															isLight
															size='sm'
															icon='Delete'>
															Delete
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>

			{/* Add Supplier Modal */}
			<Modal isOpen={isModalOpen} setIsOpen={setIsModalOpen} size='lg'>
				<ModalHeader setIsOpen={setIsModalOpen}>
					<ModalTitle>Add New Supplier</ModalTitle>
				</ModalHeader>
				<ModalBody>
					<div className='row g-4'>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='supplierName'>Supplier Name</Label>
								<Input id='supplierName' placeholder='Enter supplier name' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='contactPerson'>Contact Person</Label>
								<Input id='contactPerson' placeholder='Enter contact person name' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='phone'>Phone Number</Label>
								<Input id='phone' placeholder='Enter phone number' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='email'>Email Address</Label>
								<Input id='email' type='email' placeholder='Enter email address' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='category'>Category</Label>
								<Input id='category' list='categoryList' placeholder='Select category' />
								<datalist id='categoryList'>
									<option value='Raw Milk' />
									<option value='Packaging' />
									<option value='Animal Feed' />
									<option value='Equipment' />
									<option value='Chemicals' />
								</datalist>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='address'>Address</Label>
								<Input id='address' placeholder='Enter supplier address' />
							</FormGroup>
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color='secondary' onClick={() => setIsModalOpen(false)}>
						Cancel
					</Button>
					<Button color='primary' onClick={() => setIsModalOpen(false)}>
						Add Supplier
					</Button>
				</ModalFooter>
			</Modal>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default SuppliersPage;

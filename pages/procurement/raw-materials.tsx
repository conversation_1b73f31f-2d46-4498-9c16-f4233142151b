import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';
import Modal, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON>dalTitle } from '../../components/bootstrap/Modal';
import FormGroup from '../../components/bootstrap/forms/FormGroup';
import Input from '../../components/bootstrap/forms/Input';
import Label from '../../components/bootstrap/forms/Label';
import Select from '../../components/bootstrap/forms/Select';
import Option from '../../components/bootstrap/Option';

const RawMaterialsPage: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);
	const [isModalOpen, setIsModalOpen] = useState(false);

	// Sample raw materials data
	const rawMaterials = [
		{
			id: 1,
			name: 'Fresh Milk',
			category: 'Primary',
			currentStock: 2500,
			minStock: 1000,
			maxStock: 5000,
			unit: 'Liters',
			supplier: 'Fresh Milk Producers',
			lastUpdated: '2024-01-15',
			status: 'In Stock',
		},
		{
			id: 2,
			name: 'Milk Powder',
			category: 'Secondary',
			currentStock: 150,
			minStock: 200,
			maxStock: 800,
			unit: 'Kg',
			supplier: 'ABC Dairy Supplies',
			lastUpdated: '2024-01-14',
			status: 'Low Stock',
		},
		{
			id: 3,
			name: 'Packaging Material',
			category: 'Packaging',
			currentStock: 5000,
			minStock: 2000,
			maxStock: 10000,
			unit: 'Units',
			supplier: 'Pack Solutions Ltd',
			lastUpdated: '2024-01-13',
			status: 'In Stock',
		},
		{
			id: 4,
			name: 'Sugar',
			category: 'Additive',
			currentStock: 50,
			minStock: 100,
			maxStock: 500,
			unit: 'Kg',
			supplier: 'Sweet Suppliers',
			lastUpdated: '2024-01-12',
			status: 'Critical',
		},
	];

	const getStockPercentage = (current: number, max: number) => {
		return (current / max) * 100;
	};

	const getStockColor = (current: number, min: number) => {
		if (current <= min * 0.5) return 'danger';
		if (current <= min) return 'warning';
		return 'success';
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'In Stock': return 'success';
			case 'Low Stock': return 'warning';
			case 'Critical': return 'danger';
			case 'Out of Stock': return 'dark';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Raw Materials - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Inventory' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Raw Materials Inventory</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						onClick={() => setIsModalOpen(true)}>
						Add Material
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Inventory' /> Raw Materials Stock Status
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Material Name</th>
												<th>Category</th>
												<th>Current Stock</th>
												<th>Stock Level</th>
												<th>Supplier</th>
												<th>Status</th>
												<th>Last Updated</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{rawMaterials.map((material) => (
												<tr key={material.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Inventory' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{material.name}</div>
																<div className='text-muted small'>ID: {material.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge
															color={
																material.category === 'Primary' ? 'primary' :
																material.category === 'Secondary' ? 'info' :
																material.category === 'Packaging' ? 'success' :
																'warning'
															}
															isLight>
															{material.category}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>{material.currentStock.toLocaleString()}</div>
														<div className='text-muted small'>{material.unit}</div>
													</td>
													<td style={{ width: 150 }}>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={getStockPercentage(material.currentStock, material.maxStock)}
																	color={getStockColor(material.currentStock, material.minStock)}
																	height={8}
																/>
															</div>
															<small className='text-muted'>
																{Math.round(getStockPercentage(material.currentStock, material.maxStock))}%
															</small>
														</div>
														<div className='small text-muted mt-1'>
															Min: {material.minStock} | Max: {material.maxStock}
														</div>
													</td>
													<td>{material.supplier}</td>
													<td>
														<Badge color={getStatusColor(material.status)} isLight>
															{material.status}
														</Badge>
													</td>
													<td>{new Date(material.lastUpdated).toLocaleDateString()}</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Edit'
															className='me-2'>
															Edit
														</Button>
														<Button
															color='info'
															isLight
															size='sm'
															icon='Add'>
															Restock
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Stock Summary Cards */}
				<div className='row mt-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2</div>
										<div className='text-muted'>In Stock</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Warning' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Low Stock</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-danger'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Error' size='3x' color='danger' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Critical</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Inventory' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4</div>
										<div className='text-muted'>Total Items</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>

			{/* Add Material Modal */}
			<Modal isOpen={isModalOpen} setIsOpen={setIsModalOpen} size='lg'>
				<ModalHeader setIsOpen={setIsModalOpen}>
					<ModalTitle>Add New Raw Material</ModalTitle>
				</ModalHeader>
				<ModalBody>
					<div className='row g-4'>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='materialName'>Material Name</Label>
								<Input id='materialName' placeholder='Enter material name' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='category'>Category</Label>
								<Select id='category'>
									<Option value=''>Select Category</Option>
									<Option value='primary'>Primary</Option>
									<Option value='secondary'>Secondary</Option>
									<Option value='packaging'>Packaging</Option>
									<Option value='additive'>Additive</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-4'>
							<FormGroup>
								<Label htmlFor='currentStock'>Current Stock</Label>
								<Input id='currentStock' type='number' placeholder='0' />
							</FormGroup>
						</div>
						<div className='col-md-4'>
							<FormGroup>
								<Label htmlFor='minStock'>Minimum Stock</Label>
								<Input id='minStock' type='number' placeholder='0' />
							</FormGroup>
						</div>
						<div className='col-md-4'>
							<FormGroup>
								<Label htmlFor='maxStock'>Maximum Stock</Label>
								<Input id='maxStock' type='number' placeholder='0' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='unit'>Unit of Measurement</Label>
								<Select id='unit'>
									<Option value=''>Select Unit</Option>
									<Option value='kg'>Kilograms (Kg)</Option>
									<Option value='liters'>Liters</Option>
									<Option value='units'>Units</Option>
									<Option value='tons'>Tons</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='supplier'>Primary Supplier</Label>
								<Select id='supplier'>
									<Option value=''>Select Supplier</Option>
									<Option value='abc'>ABC Dairy Supplies</Option>
									<Option value='fresh'>Fresh Milk Producers</Option>
									<Option value='quality'>Quality Feed Solutions</Option>
								</Select>
							</FormGroup>
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color='secondary' onClick={() => setIsModalOpen(false)}>
						Cancel
					</Button>
					<Button color='primary' onClick={() => setIsModalOpen(false)}>
						Add Material
					</Button>
				</ModalFooter>
			</Modal>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default RawMaterialsPage;

import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import { dairyERPMainMenu } from '../../menu';

const ProcurementDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	return (
		<PageWrapper>
			<Head>
				<title>Procurement Dashboard - A<PERSON><PERSON> Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='ShoppingCart' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Procurement Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						tag='a'
						to='/procurement/purchase-orders'>
						New Purchase Order
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Dashboard' /> Procurement Overview
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-4'>
									<div className='col-xl-3 col-md-6'>
										<Card className='bg-l10-primary'>
											<CardBody>
												<div className='d-flex align-items-center'>
													<div className='flex-shrink-0'>
														<Icon icon='Store' size='3x' color='primary' />
													</div>
													<div className='flex-grow-1 ms-3'>
														<div className='fw-bold h4 mb-0'>24</div>
														<div className='text-muted'>Active Suppliers</div>
													</div>
												</div>
											</CardBody>
										</Card>
									</div>
									<div className='col-xl-3 col-md-6'>
										<Card className='bg-l10-success'>
											<CardBody>
												<div className='d-flex align-items-center'>
													<div className='flex-shrink-0'>
														<Icon icon='Receipt' size='3x' color='success' />
													</div>
													<div className='flex-grow-1 ms-3'>
														<div className='fw-bold h4 mb-0'>12</div>
														<div className='text-muted'>Pending Orders</div>
													</div>
												</div>
											</CardBody>
										</Card>
									</div>
									<div className='col-xl-3 col-md-6'>
										<Card className='bg-l10-warning'>
											<CardBody>
												<div className='d-flex align-items-center'>
													<div className='flex-shrink-0'>
														<Icon icon='Inventory' size='3x' color='warning' />
													</div>
													<div className='flex-grow-1 ms-3'>
														<div className='fw-bold h4 mb-0'>8</div>
														<div className='text-muted'>Low Stock Items</div>
													</div>
												</div>
											</CardBody>
										</Card>
									</div>
									<div className='col-xl-3 col-md-6'>
										<Card className='bg-l10-info'>
											<CardBody>
												<div className='d-flex align-items-center'>
													<div className='flex-shrink-0'>
														<Icon icon='TrendingUp' size='3x' color='info' />
													</div>
													<div className='flex-grow-1 ms-3'>
														<div className='fw-bold h4 mb-0'>₹2.4L</div>
														<div className='text-muted'>Monthly Spend</div>
													</div>
												</div>
											</CardBody>
										</Card>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				<div className='row mt-4'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Quick Actions</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/procurement/suppliers'>
											<Icon icon='Store' className='me-2' />
											Manage Suppliers
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/procurement/purchase-orders'>
											<Icon icon='Receipt' className='me-2' />
											Purchase Orders
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/procurement/raw-materials'>
											<Icon icon='Inventory' className='me-2' />
											Raw Materials
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/procurement/dashboard'>
											<Icon icon='Analytics' className='me-2' />
											Reports
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New supplier registered</h6>
											<p className='timeline-text'>ABC Dairy Supplies added to vendor list</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Purchase order approved</h6>
											<p className='timeline-text'>PO #PO-2024-001 for milk packaging materials</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Low stock alert</h6>
											<p className='timeline-text'>Milk powder inventory below threshold</p>
											<small className='text-muted'>6 hours ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default ProcurementDashboard;

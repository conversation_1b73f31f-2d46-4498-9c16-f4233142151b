import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Modal, { ModalBody, ModalFooter, ModalHeader, ModalTitle } from '../../components/bootstrap/Modal';
import FormGroup from '../../components/bootstrap/forms/FormGroup';
import Input from '../../components/bootstrap/forms/Input';
import Label from '../../components/bootstrap/forms/Label';
import Select from '../../components/bootstrap/forms/Select';
import Option from '../../components/bootstrap/Option';

const PurchaseOrdersPage: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);
	const [isModalOpen, setIsModalOpen] = useState(false);

	// Sample purchase order data
	const purchaseOrders = [
		{
			id: 'PO-2024-001',
			supplier: 'ABC Dairy Supplies',
			date: '2024-01-15',
			amount: 25000,
			status: 'Pending',
			items: 5,
			deliveryDate: '2024-01-20',
		},
		{
			id: 'PO-2024-002',
			supplier: 'Fresh Milk Producers',
			date: '2024-01-14',
			amount: 45000,
			status: 'Approved',
			items: 3,
			deliveryDate: '2024-01-18',
		},
		{
			id: 'PO-2024-003',
			supplier: 'Quality Feed Solutions',
			date: '2024-01-13',
			amount: 18000,
			status: 'Delivered',
			items: 8,
			deliveryDate: '2024-01-17',
		},
		{
			id: 'PO-2024-004',
			supplier: 'Equipment Suppliers Ltd',
			date: '2024-01-12',
			amount: 75000,
			status: 'Rejected',
			items: 2,
			deliveryDate: '2024-01-25',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Pending': return 'warning';
			case 'Approved': return 'info';
			case 'Delivered': return 'success';
			case 'Rejected': return 'danger';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Purchase Orders - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Receipt' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Purchase Orders</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						onClick={() => setIsModalOpen(true)}>
						Create Purchase Order
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Receipt' /> Purchase Order Management
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>PO Number</th>
												<th>Supplier</th>
												<th>Date</th>
												<th>Amount</th>
												<th>Items</th>
												<th>Delivery Date</th>
												<th>Status</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{purchaseOrders.map((po) => (
												<tr key={po.id}>
													<td>
														<div className='fw-bold text-primary'>{po.id}</div>
													</td>
													<td>{po.supplier}</td>
													<td>{new Date(po.date).toLocaleDateString()}</td>
													<td>
														<span className='fw-bold'>₹{po.amount.toLocaleString()}</span>
													</td>
													<td>
														<Badge color='info' isLight>
															{po.items} items
														</Badge>
													</td>
													<td>{new Date(po.deliveryDate).toLocaleDateString()}</td>
													<td>
														<Badge color={getStatusColor(po.status)} isLight>
															{po.status}
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'
															className='me-2'>
															Edit
														</Button>
														<Button
															color='danger'
															isLight
															size='sm'
															icon='Delete'>
															Delete
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Summary Cards */}
				<div className='row mt-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='HourglassTop' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2</div>
										<div className='text-muted'>Pending Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Approved Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='LocalShipping' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Delivered Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹1.63L</div>
										<div className='text-muted'>Total Value</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>

			{/* Create Purchase Order Modal */}
			<Modal isOpen={isModalOpen} setIsOpen={setIsModalOpen} size='xl'>
				<ModalHeader setIsOpen={setIsModalOpen}>
					<ModalTitle>Create New Purchase Order</ModalTitle>
				</ModalHeader>
				<ModalBody>
					<div className='row g-4'>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='supplier'>Supplier</Label>
								<Select id='supplier'>
									<Option value=''>Select Supplier</Option>
									<Option value='abc'>ABC Dairy Supplies</Option>
									<Option value='fresh'>Fresh Milk Producers</Option>
									<Option value='quality'>Quality Feed Solutions</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='deliveryDate'>Expected Delivery Date</Label>
								<Input id='deliveryDate' type='date' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='priority'>Priority</Label>
								<Select id='priority'>
									<Option value='low'>Low</Option>
									<Option value='medium'>Medium</Option>
									<Option value='high'>High</Option>
									<Option value='urgent'>Urgent</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='department'>Requesting Department</Label>
								<Select id='department'>
									<Option value='production'>Production</Option>
									<Option value='packaging'>Packaging</Option>
									<Option value='quality'>Quality Control</Option>
									<Option value='maintenance'>Maintenance</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-12'>
							<FormGroup>
								<Label htmlFor='notes'>Notes</Label>
								<Input id='notes' placeholder='Additional notes or requirements' />
							</FormGroup>
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color='secondary' onClick={() => setIsModalOpen(false)}>
						Cancel
					</Button>
					<Button color='primary' onClick={() => setIsModalOpen(false)}>
						Create Purchase Order
					</Button>
				</ModalFooter>
			</Modal>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default PurchaseOrdersPage;

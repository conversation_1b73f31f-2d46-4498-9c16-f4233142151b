import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const SecurityDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample security data
	const securityAlerts = [
		{
			id: 'SEC-001',
			type: 'Failed Login',
			description: 'Multiple failed login attempts from IP *************',
			severity: 'Medium',
			status: 'Active',
			timestamp: '2024-01-15 14:30:25',
			user: 'Unknown',
			action: 'Account locked',
		},
		{
			id: 'SEC-002',
			type: 'Privilege Escalation',
			description: 'User attempted to access admin panel without permission',
			severity: 'High',
			status: 'Resolved',
			timestamp: '2024-01-15 12:15:10',
			user: '<EMAIL>',
			action: 'Access denied',
		},
		{
			id: 'SEC-003',
			type: 'Data Export',
			description: 'Large data export operation detected',
			severity: 'Low',
			status: 'Monitoring',
			timestamp: '2024-01-15 10:45:30',
			user: '<EMAIL>',
			action: 'Logged for audit',
		},
	];

	const userActivities = [
		{
			user: 'Rajesh Kumar',
			action: 'Login',
			module: 'Procurement',
			timestamp: '2024-01-15 15:30:00',
			ip: '*************',
			status: 'Success',
		},
		{
			user: 'Priya Sharma',
			action: 'Data Update',
			module: 'Sales',
			timestamp: '2024-01-15 15:25:00',
			ip: '*************',
			status: 'Success',
		},
		{
			user: 'Amit Singh',
			action: 'Report Generation',
			module: 'Inventory',
			timestamp: '2024-01-15 15:20:00',
			ip: '*************',
			status: 'Success',
		},
	];

	const getSeverityColor = (severity: string) => {
		switch (severity) {
			case 'Critical': return 'danger';
			case 'High': return 'warning';
			case 'Medium': return 'info';
			case 'Low': return 'success';
			default: return 'secondary';
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'danger';
			case 'Resolved': return 'success';
			case 'Monitoring': return 'warning';
			case 'Dismissed': return 'secondary';
			default: return 'info';
		}
	};

	const getActionStatusColor = (status: string) => {
		switch (status) {
			case 'Success': return 'success';
			case 'Failed': return 'danger';
			case 'Blocked': return 'warning';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Security - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Security' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Security Center</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Refresh'
						color='primary'
						isLight
						className='me-2'>
						Refresh Status
					</Button>
					<Button
						icon='Shield'
						color='success'
						isLight>
						Security Scan
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Security Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Shield' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>98%</div>
										<div className='text-muted'>Security Score</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Excellent security
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Warning' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>3</div>
										<div className='text-muted'>Active Alerts</div>
										<div className='small text-warning'>
											<Icon icon='Notifications' className='me-1' />
											Requires attention
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>42</div>
										<div className='text-muted'>Active Sessions</div>
										<div className='small text-info'>
											<Icon icon='Computer' className='me-1' />
											Currently online
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Lock' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>0</div>
										<div className='text-muted'>Breaches Today</div>
										<div className='small text-success'>
											<Icon icon='CheckCircle' className='me-1' />
											System secure
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Security Alerts and User Activity */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Warning' /> Security Alerts
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Alert</th>
												<th>Severity</th>
												<th>Status</th>
												<th>User</th>
												<th>Timestamp</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{securityAlerts.map((alert) => (
												<tr key={alert.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-warning rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Warning' size='lg' color='warning' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{alert.type}</div>
																<div className='text-muted small'>{alert.id}</div>
																<div className='text-muted small'>{alert.description}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getSeverityColor(alert.severity)} isLight>
															{alert.severity}
														</Badge>
													</td>
													<td>
														<Badge color={getStatusColor(alert.status)} isLight>
															{alert.status}
														</Badge>
													</td>
													<td>
														<div className='text-muted small'>{alert.user}</div>
													</td>
													<td>
														<div className='text-muted small'>{alert.timestamp}</div>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Check'>
															Resolve
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent User Activity</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									{userActivities.map((activity, index) => (
										<div key={index} className='timeline-item'>
											<div className={`timeline-marker bg-${getActionStatusColor(activity.status)}`}></div>
											<div className='timeline-content'>
												<h6 className='timeline-title'>{activity.user}</h6>
												<p className='timeline-text'>
													{activity.action} in {activity.module}
												</p>
												<div className='d-flex justify-content-between align-items-center'>
													<small className='text-muted'>{activity.timestamp}</small>
													<Badge color={getActionStatusColor(activity.status)} isLight>
														{activity.status}
													</Badge>
												</div>
												<div className='text-muted small'>IP: {activity.ip}</div>
											</div>
										</div>
									))}
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Security Metrics */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Security Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/security/alerts'>
											<Icon icon='Warning' className='me-2' />
											Alerts
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/security/audit'>
											<Icon icon='Assessment' className='me-2' />
											Audit Logs
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/security/policies'>
											<Icon icon='Policy' className='me-2' />
											Policies
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/security/monitoring'>
											<Icon icon='Monitor' className='me-2' />
											Monitoring
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/security/compliance'>
											<Icon icon='VerifiedUser' className='me-2' />
											Compliance
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/security/settings'>
											<Icon icon='Settings' className='me-2' />
											Settings
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Security Metrics</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-12'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Password Strength</span>
											<Badge color='success' isLight>Strong</Badge>
										</div>
										<Progress value={92} color='success' height={8} />
									</div>
									<div className='col-12'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Two-Factor Authentication</span>
											<Badge color='warning' isLight>Partial</Badge>
										</div>
										<Progress value={68} color='warning' height={8} />
									</div>
									<div className='col-12'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Access Control</span>
											<Badge color='success' isLight>Excellent</Badge>
										</div>
										<Progress value={95} color='success' height={8} />
									</div>
									<div className='col-12'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Data Encryption</span>
											<Badge color='success' isLight>Active</Badge>
										</div>
										<Progress value={100} color='success' height={8} />
									</div>
									<div className='col-12'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Vulnerability Assessment</span>
											<Badge color='info' isLight>Scheduled</Badge>
										</div>
										<Progress value={85} color='info' height={8} />
									</div>
								</div>
								<div className='mt-3'>
									<Button
										color='primary'
										isLight
										className='w-100'
										icon='Security'>
										Run Security Scan
									</Button>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default SecurityDashboard;

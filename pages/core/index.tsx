import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const CoreSystemDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample system data
	const systemModules = [
		{
			id: 'MOD-001',
			name: 'User Management',
			description: 'Manage users, roles, and permissions',
			status: 'Active',
			version: '2.1.0',
			lastUpdated: '2024-01-15',
			health: 98,
			users: 156,
		},
		{
			id: 'MOD-002',
			name: 'Database Management',
			description: 'Database configuration and monitoring',
			status: 'Active',
			version: '1.8.5',
			lastUpdated: '2024-01-12',
			health: 95,
			users: 12,
		},
		{
			id: 'MOD-003',
			name: 'Backup & Recovery',
			description: 'System backup and disaster recovery',
			status: 'Active',
			version: '3.0.2',
			lastUpdated: '2024-01-10',
			health: 92,
			users: 5,
		},
		{
			id: 'MOD-004',
			name: 'System Monitoring',
			description: 'Performance monitoring and alerts',
			status: 'Maintenance',
			version: '2.5.1',
			lastUpdated: '2024-01-08',
			health: 88,
			users: 8,
		},
	];

	const systemStats = [
		{
			metric: 'CPU Usage',
			value: 45,
			unit: '%',
			status: 'Good',
			color: 'success',
		},
		{
			metric: 'Memory Usage',
			value: 68,
			unit: '%',
			status: 'Normal',
			color: 'warning',
		},
		{
			metric: 'Disk Usage',
			value: 32,
			unit: '%',
			status: 'Good',
			color: 'success',
		},
		{
			metric: 'Network Load',
			value: 78,
			unit: '%',
			status: 'High',
			color: 'danger',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'Maintenance': return 'warning';
			case 'Inactive': return 'danger';
			case 'Updating': return 'info';
			default: return 'secondary';
		}
	};

	const getHealthColor = (health: number) => {
		if (health >= 95) return 'success';
		if (health >= 85) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Core System - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Settings' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Core System</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Refresh'
						color='primary'
						isLight
						className='me-2'>
						Refresh Status
					</Button>
					<Button
						icon='Build'
						color='success'
						isLight>
						System Maintenance
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* System Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Computer' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>99.8%</div>
										<div className='text-muted'>System Uptime</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Excellent performance
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>156</div>
										<div className='text-muted'>Active Users</div>
										<div className='small text-info'>
											<Icon icon='Person' className='me-1' />
											Currently online: 42
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Storage' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2.8TB</div>
										<div className='text-muted'>Data Storage</div>
										<div className='small text-warning'>
											<Icon icon='Warning' className='me-1' />
											68% capacity used
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Security' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>0</div>
										<div className='text-muted'>Security Alerts</div>
										<div className='small text-success'>
											<Icon icon='CheckCircle' className='me-1' />
											All systems secure
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* System Performance and Module Status */}
				<div className='row mb-4'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Speed' /> System Performance
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									{systemStats.map((stat, index) => (
										<div key={index} className='col-6'>
											<div className={`p-3 bg-l10-${stat.color} rounded`}>
												<div className='d-flex justify-content-between align-items-center mb-2'>
													<span className='fw-bold'>{stat.metric}</span>
													<Badge color={stat.color} isLight>
														{stat.status}
													</Badge>
												</div>
												<div className='d-flex align-items-center'>
													<div className='flex-grow-1 me-2'>
														<Progress
															value={stat.value}
															color={stat.color}
															height={8}
														/>
													</div>
													<span className='fw-bold'>
														{stat.value}{stat.unit}
													</span>
												</div>
											</div>
										</div>
									))}
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>System Modules</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Module</th>
												<th>Status</th>
												<th>Health</th>
												<th>Version</th>
											</tr>
										</thead>
										<tbody>
											{systemModules.map((module) => (
												<tr key={module.id}>
													<td>
														<div>
															<div className='fw-bold'>{module.name}</div>
															<div className='text-muted small'>{module.description}</div>
														</div>
													</td>
													<td>
														<Badge color={getStatusColor(module.status)} isLight>
															{module.status}
														</Badge>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={module.health}
																	color={getHealthColor(module.health)}
																	height={6}
																/>
															</div>
															<small className='fw-bold'>
																{module.health}%
															</small>
														</div>
													</td>
													<td>
														<span className='text-muted small'>{module.version}</span>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and System Logs */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>System Administration</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/core/users'>
											<Icon icon='People' className='me-2' />
											Users
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/core/database'>
											<Icon icon='Storage' className='me-2' />
											Database
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/core/backup'>
											<Icon icon='Backup' className='me-2' />
											Backup
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/core/monitoring'>
											<Icon icon='Monitor' className='me-2' />
											Monitoring
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/core/configuration'>
											<Icon icon='Settings' className='me-2' />
											Configuration
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/core/logs'>
											<Icon icon='Description' className='me-2' />
											System Logs
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent System Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>System backup completed</h6>
											<p className='timeline-text'>Daily backup completed successfully - 2.8TB</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>User login</h6>
											<p className='timeline-text'>Administrator logged in from 192.168.1.100</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>System maintenance</h6>
											<p className='timeline-text'>Monitoring module updated to version 2.5.1</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Database optimization</h6>
											<p className='timeline-text'>Database performance optimization completed</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default CoreSystemDashboard;

import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const FSSAIDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample FSSAI compliance data
	const complianceItems = [
		{
			id: 1,
			category: 'License & Registration',
			item: 'FSSAI License Renewal',
			status: 'Valid',
			expiryDate: '2024-12-31',
			daysToExpiry: 350,
			priority: 'Low',
		},
		{
			id: 2,
			category: 'Quality Testing',
			item: 'Monthly Milk Quality Test',
			status: 'Due',
			expiryDate: '2024-01-20',
			daysToExpiry: 5,
			priority: 'High',
		},
		{
			id: 3,
			category: 'Documentation',
			item: 'HACCP Plan Review',
			status: 'Overdue',
			expiryDate: '2024-01-10',
			daysToExpiry: -5,
			priority: 'Critical',
		},
		{
			id: 4,
			category: 'Training',
			item: 'Food Safety Training',
			status: 'Valid',
			expiryDate: '2024-06-15',
			daysToExpiry: 156,
			priority: 'Medium',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Valid': return 'success';
			case 'Due': return 'warning';
			case 'Overdue': return 'danger';
			case 'Expired': return 'dark';
			default: return 'secondary';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'Critical': return 'danger';
			case 'High': return 'warning';
			case 'Medium': return 'info';
			case 'Low': return 'success';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>FSSAI Compliance - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='VerifiedUser' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>FSSAI Compliance Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Compliance Item
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						Generate Report
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Compliance Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>85%</div>
										<div className='text-muted'>Compliance Score</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+5% this month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Warning' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2</div>
										<div className='text-muted'>Items Due</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											Action required
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-danger'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Error' size='3x' color='danger' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Overdue Items</div>
										<div className='small text-danger'>
											<Icon icon='PriorityHigh' className='me-1' />
											Immediate action
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Assignment' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>12</div>
										<div className='text-muted'>Total Items</div>
										<div className='small text-info'>
											<Icon icon='Visibility' className='me-1' />
											All categories
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Compliance Items Table */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='VerifiedUser' /> Compliance Tracking
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Category</th>
												<th>Compliance Item</th>
												<th>Status</th>
												<th>Expiry Date</th>
												<th>Days to Expiry</th>
												<th>Priority</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{complianceItems.map((item) => (
												<tr key={item.id}>
													<td>
														<Badge
															color={
																item.category === 'License & Registration' ? 'primary' :
																item.category === 'Quality Testing' ? 'success' :
																item.category === 'Documentation' ? 'info' :
																'warning'
															}
															isLight>
															{item.category}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>{item.item}</div>
													</td>
													<td>
														<Badge color={getStatusColor(item.status)} isLight>
															{item.status}
														</Badge>
													</td>
													<td>{new Date(item.expiryDate).toLocaleDateString()}</td>
													<td>
														<div className='d-flex align-items-center'>
															<span className={`fw-bold ${
																item.daysToExpiry < 0 ? 'text-danger' :
																item.daysToExpiry <= 30 ? 'text-warning' :
																'text-success'
															}`}>
																{item.daysToExpiry < 0 ? 
																	`${Math.abs(item.daysToExpiry)} days overdue` :
																	`${item.daysToExpiry} days`
																}
															</span>
														</div>
													</td>
													<td>
														<Badge color={getPriorityColor(item.priority)} isLight>
															{item.priority}
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Edit'
															className='me-2'>
															Update
														</Button>
														<Button
															color='info'
															isLight
															size='sm'
															icon='Visibility'>
															View
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>FSSAI Quick Actions</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai/licenses'>
											<Icon icon='Assignment' className='me-2' />
											Licenses
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai/testing'>
											<Icon icon='Science' className='me-2' />
											Quality Testing
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai/documentation'>
											<Icon icon='Description' className='me-2' />
											Documentation
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai/training'>
											<Icon icon='School' className='me-2' />
											Training
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai/audits'>
											<Icon icon='FindInPage' className='me-2' />
											Audits
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai/reports'>
											<Icon icon='Assessment' className='me-2' />
											Reports
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Compliance Alerts</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-danger'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>HACCP Plan Review Overdue</h6>
											<p className='timeline-text'>Critical compliance item requires immediate attention</p>
											<small className='text-muted'>5 days overdue</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Monthly Quality Test Due</h6>
											<p className='timeline-text'>Milk quality testing scheduled for this week</p>
											<small className='text-muted'>Due in 5 days</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Training Completed</h6>
											<p className='timeline-text'>Food safety training for production staff completed</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>License Renewed</h6>
											<p className='timeline-text'>FSSAI license successfully renewed for 2024</p>
											<small className='text-muted'>1 week ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default FSSAIDashboard;

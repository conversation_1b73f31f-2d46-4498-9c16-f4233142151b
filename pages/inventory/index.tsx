import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';
import Chart, { IChartOptions } from '../../components/extras/Chart';

const InventoryDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample inventory data
	const inventoryItems = [
		{
			id: 1,
			name: 'Whole Milk (1L)',
			category: 'Finished Products',
			currentStock: 2500,
			minStock: 1000,
			maxStock: 5000,
			unit: 'Units',
			value: 125000,
			location: 'Cold Storage A',
		},
		{
			id: 2,
			name: 'Greek Yogurt (500g)',
			category: 'Finished Products',
			currentStock: 800,
			minStock: 500,
			maxStock: 2000,
			unit: 'Units',
			value: 48000,
			location: 'Cold Storage B',
		},
		{
			id: 3,
			name: 'Cheddar Cheese (200g)',
			category: 'Finished Products',
			currentStock: 150,
			minStock: 200,
			maxStock: 800,
			unit: 'Units',
			value: 22500,
			location: 'Cold Storage C',
		},
		{
			id: 4,
			name: 'Packaging Materials',
			category: 'Raw Materials',
			currentStock: 5000,
			minStock: 2000,
			maxStock: 10000,
			unit: 'Units',
			value: 50000,
			location: 'Warehouse A',
		},
	];

	// Chart data for inventory analytics
	const inventoryTrendData: IChartOptions = {
		series: [
			{
				name: 'Stock Level',
				data: [85, 78, 92, 88, 76, 85],
			},
		],
		options: {
			chart: {
				type: 'line',
				height: 300,
			},
			xaxis: {
				categories: ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
			},
			yaxis: {
				labels: {
					formatter: (value) => `${value}%`,
				},
			},
			stroke: {
				curve: 'smooth',
			},
			colors: ['#3b82f6'],
		},
	};

	const getStockPercentage = (current: number, max: number) => {
		return (current / max) * 100;
	};

	const getStockColor = (current: number, min: number) => {
		if (current <= min * 0.5) return 'danger';
		if (current <= min) return 'warning';
		return 'success';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Inventory Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Inventory' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Inventory Management Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Item
					</Button>
					<Button
						icon='Sync'
						color='success'
						isLight>
						Stock Take
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Inventory Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Inventory' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>8,450</div>
										<div className='text-muted'>Total Items</div>
										<div className='small text-info'>
											<Icon icon='Category' className='me-1' />
											4 categories
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹2.45L</div>
										<div className='text-muted'>Total Value</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+8% this month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Warning' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Low Stock Items</div>
										<div className='small text-warning'>
											<Icon icon='PriorityHigh' className='me-1' />
											Reorder required
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='TrendingUp' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>92%</div>
										<div className='text-muted'>Stock Efficiency</div>
										<div className='small text-success'>
											<Icon icon='CheckCircle' className='me-1' />
											Optimal level
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Inventory Items and Chart */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Inventory' /> Current Inventory Status
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Item Name</th>
												<th>Category</th>
												<th>Stock Level</th>
												<th>Current Stock</th>
												<th>Value</th>
												<th>Location</th>
											</tr>
										</thead>
										<tbody>
											{inventoryItems.map((item) => (
												<tr key={item.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Inventory' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{item.name}</div>
																<div className='text-muted small'>ID: {item.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge
															color={item.category === 'Finished Products' ? 'success' : 'info'}
															isLight>
															{item.category}
														</Badge>
													</td>
													<td style={{ width: 150 }}>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={getStockPercentage(item.currentStock, item.maxStock)}
																	color={getStockColor(item.currentStock, item.minStock)}
																	height={8}
																/>
															</div>
															<small className='text-muted'>
																{Math.round(getStockPercentage(item.currentStock, item.maxStock))}%
															</small>
														</div>
														<div className='small text-muted mt-1'>
															Min: {item.minStock} | Max: {item.maxStock}
														</div>
													</td>
													<td>
														<div className='fw-bold'>{item.currentStock.toLocaleString()}</div>
														<div className='text-muted small'>{item.unit}</div>
													</td>
													<td>
														<div className='fw-bold'>₹{item.value.toLocaleString()}</div>
													</td>
													<td>{item.location}</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Stock Level Trend</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={inventoryTrendData.series}
									options={inventoryTrendData.options}
									type='line'
									height={300}
								/>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Inventory Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory/items'>
											<Icon icon='Inventory' className='me-2' />
											Items
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory/movements'>
											<Icon icon='SwapHoriz' className='me-2' />
											Movements
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory/locations'>
											<Icon icon='LocationOn' className='me-2' />
											Locations
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory/reports'>
											<Icon icon='Assessment' className='me-2' />
											Reports
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory/adjustments'>
											<Icon icon='Tune' className='me-2' />
											Adjustments
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory/alerts'>
											<Icon icon='NotificationsActive' className='me-2' />
											Alerts
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Inventory Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Low stock alert</h6>
											<p className='timeline-text'>Cheddar Cheese below minimum threshold</p>
											<small className='text-muted'>1 hour ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Stock received</h6>
											<p className='timeline-text'>2000 units of packaging materials added</p>
											<small className='text-muted'>3 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Stock movement</h6>
											<p className='timeline-text'>500 units transferred to Cold Storage B</p>
											<small className='text-muted'>5 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Stock adjustment</h6>
											<p className='timeline-text'>Inventory count updated for Whole Milk</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default InventoryDashboard;

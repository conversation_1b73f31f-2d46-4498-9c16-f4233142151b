import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const DeliveryTracking: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample delivery data
	const deliveryData = [
		{
			id: 'DEL001',
			orderId: 'ORD-2024-001',
			customer: 'Metro Stores',
			vehicle: 'MH-12-AB-1234',
			driver: 'Rajesh Kumar',
			route: 'Route A',
			status: 'In Transit',
			progress: 65,
			estimatedDelivery: '10:30 AM',
			actualDelivery: null,
			items: '500L Fresh Milk, 200L Yogurt',
		},
		{
			id: 'DEL002',
			orderId: 'ORD-2024-002',
			customer: 'Big Bazaar',
			vehicle: 'MH-12-CD-5678',
			driver: 'Suresh Patil',
			route: 'Route B',
			status: 'Delivered',
			progress: 100,
			estimatedDelivery: '09:00 AM',
			actualDelivery: '08:45 AM',
			items: '300L Fresh Milk, 150L Buttermilk',
		},
		{
			id: 'DEL003',
			orderId: 'ORD-2024-003',
			customer: 'D-Mart',
			vehicle: 'MH-12-EF-9012',
			driver: 'Amit Sharma',
			route: 'Route C',
			status: 'Pending',
			progress: 0,
			estimatedDelivery: '11:00 AM',
			actualDelivery: null,
			items: '400L Fresh Milk, 100L Cream',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Delivered': return 'success';
			case 'In Transit': return 'info';
			case 'Pending': return 'warning';
			case 'Delayed': return 'danger';
			default: return 'secondary';
		}
	};

	const getProgressColor = (progress: number) => {
		if (progress === 100) return 'success';
		if (progress > 50) return 'info';
		if (progress > 0) return 'warning';
		return 'secondary';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Delivery Tracking - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='LocalShipping' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Delivery Tracking</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Refresh'
						color='primary'
						isLight
						className='me-2'>
						Refresh
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						Delivery Report
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Delivery Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Assignment' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>15</div>
										<div className='text-muted'>Total Deliveries</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>8</div>
										<div className='text-muted'>Completed</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='LocalShipping' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>5</div>
										<div className='text-muted'>In Transit</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Schedule' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2</div>
										<div className='text-muted'>Pending</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Delivery Tracking Table */}
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='LocalShipping' /> Live Delivery Tracking
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Delivery ID</th>
												<th>Order ID</th>
												<th>Customer</th>
												<th>Vehicle</th>
												<th>Driver</th>
												<th>Route</th>
												<th>Status</th>
												<th>Progress</th>
												<th>Est. Delivery</th>
												<th>Actual Delivery</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{deliveryData.map((delivery) => (
												<tr key={delivery.id}>
													<td className='fw-bold'>{delivery.id}</td>
													<td>{delivery.orderId}</td>
													<td>{delivery.customer}</td>
													<td>{delivery.vehicle}</td>
													<td>{delivery.driver}</td>
													<td>{delivery.route}</td>
													<td>
														<Badge color={getStatusColor(delivery.status)} isLight>
															{delivery.status}
														</Badge>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<Progress 
																value={delivery.progress} 
																color={getProgressColor(delivery.progress)}
																height={8}
																className='flex-grow-1 me-2'
															/>
															<span className='small'>{delivery.progress}%</span>
														</div>
													</td>
													<td>{delivery.estimatedDelivery}</td>
													<td>{delivery.actualDelivery || '-'}</td>
													<td>
														<Button
															icon='Visibility'
															color='info'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='LocationOn'
															color='primary'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Phone'
															color='success'
															isLight
															size='sm'>
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Delivery Items Details */}
				<div className='row mt-4'>
					<div className='col-12'>
						<Card>
							<CardHeader>
								<CardTitle>Delivery Items</CardTitle>
							</CardHeader>
							<CardBody>
								{deliveryData.map((delivery) => (
									<div key={delivery.id} className='border-bottom pb-3 mb-3'>
										<div className='d-flex justify-content-between align-items-center'>
											<div>
												<h6 className='mb-1'>{delivery.id} - {delivery.customer}</h6>
												<p className='text-muted mb-0'>{delivery.items}</p>
											</div>
											<Badge color={getStatusColor(delivery.status)} isLight>
												{delivery.status}
											</Badge>
										</div>
									</div>
								))}
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default DeliveryTracking;

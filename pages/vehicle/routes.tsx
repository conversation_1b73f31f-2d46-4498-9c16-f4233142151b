import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Badge from '../../components/bootstrap/Badge';

const RouteOptimization: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample route data
	const routeData = [
		{
			id: 'ROUTE001',
			name: 'Route A - Pune Central',
			vehicle: 'MH-12-AB-1234',
			driver: '<PERSON><PERSON>',
			stops: 8,
			distance: '45 km',
			estimatedTime: '3.5 hours',
			status: 'Active',
			efficiency: 92,
			fuelConsumption: '12.5L',
			customers: ['Metro Stores', 'Big Bazaar', 'D-Mart', 'Local Shops'],
		},
		{
			id: 'ROUTE002',
			name: 'Route B - Mumbai Suburbs',
			vehicle: 'MH-12-CD-5678',
			driver: 'Suresh Patil',
			stops: 12,
			distance: '68 km',
			estimatedTime: '4.2 hours',
			status: 'In Transit',
			efficiency: 88,
			fuelConsumption: '18.2L',
			customers: ['Reliance Fresh', 'More Supermarket', 'Spencer\'s', 'Retail Outlets'],
		},
		{
			id: 'ROUTE003',
			name: 'Route C - Nashik Region',
			vehicle: 'MH-12-EF-9012',
			driver: 'Amit Sharma',
			stops: 6,
			distance: '32 km',
			estimatedTime: '2.8 hours',
			status: 'Planned',
			efficiency: 95,
			fuelConsumption: '9.8L',
			customers: ['Wholesale Market', 'Distribution Centers', 'Retail Chains'],
		},
	];

	const optimizationSuggestions = [
		{
			route: 'Route A',
			suggestion: 'Reorder stops to reduce travel time by 15 minutes',
			savings: '₹450/day',
			priority: 'High',
		},
		{
			route: 'Route B',
			suggestion: 'Combine with nearby delivery points',
			savings: '₹320/day',
			priority: 'Medium',
		},
		{
			route: 'Route C',
			suggestion: 'Optimize timing to avoid traffic congestion',
			savings: '₹180/day',
			priority: 'Low',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'In Transit': return 'info';
			case 'Planned': return 'warning';
			case 'Completed': return 'primary';
			default: return 'secondary';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'High': return 'danger';
			case 'Medium': return 'warning';
			case 'Low': return 'success';
			default: return 'secondary';
		}
	};

	const getEfficiencyColor = (efficiency: number) => {
		if (efficiency >= 90) return 'success';
		if (efficiency >= 80) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Route Optimization - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Route' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Route Optimization</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Create Route
					</Button>
					<Button
						icon='AutoFixHigh'
						color='success'
						isLight
						className='me-2'>
						Auto Optimize
					</Button>
					<Button
						icon='Assessment'
						color='info'
						isLight>
						Route Analytics
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Route Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Route' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>15</div>
										<div className='text-muted'>Total Routes</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>8</div>
										<div className='text-muted'>Active Routes</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Savings' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹2.5L</div>
										<div className='text-muted'>Monthly Savings</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Speed' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>89%</div>
										<div className='text-muted'>Avg Efficiency</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				<div className='row'>
					{/* Route Details */}
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Route' /> Route Details
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Route ID</th>
												<th>Route Name</th>
												<th>Vehicle</th>
												<th>Driver</th>
												<th>Stops</th>
												<th>Distance</th>
												<th>Est. Time</th>
												<th>Status</th>
												<th>Efficiency</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{routeData.map((route) => (
												<tr key={route.id}>
													<td className='fw-bold'>{route.id}</td>
													<td>{route.name}</td>
													<td>{route.vehicle}</td>
													<td>{route.driver}</td>
													<td>{route.stops}</td>
													<td>{route.distance}</td>
													<td>{route.estimatedTime}</td>
													<td>
														<Badge color={getStatusColor(route.status)} isLight>
															{route.status}
														</Badge>
													</td>
													<td>
														<Badge color={getEfficiencyColor(route.efficiency)} isLight>
															{route.efficiency}%
														</Badge>
													</td>
													<td>
														<Button
															icon='Visibility'
															color='info'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Edit'
															color='primary'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='AutoFixHigh'
															color='success'
															isLight
															size='sm'>
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>

					{/* Optimization Suggestions */}
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='AutoFixHigh' /> Optimization Suggestions
								</CardTitle>
							</CardHeader>
							<CardBody>
								{optimizationSuggestions.map((suggestion, index) => (
									<div key={index} className='border-bottom pb-3 mb-3'>
										<div className='d-flex justify-content-between align-items-start mb-2'>
											<h6 className='mb-0'>{suggestion.route}</h6>
											<Badge color={getPriorityColor(suggestion.priority)} isLight>
												{suggestion.priority}
											</Badge>
										</div>
										<p className='text-muted mb-2'>{suggestion.suggestion}</p>
										<div className='d-flex justify-content-between align-items-center'>
											<small className='text-success fw-bold'>{suggestion.savings}</small>
											<Button color='primary' size='sm' isLight>
												Apply
											</Button>
										</div>
									</div>
								))}
								<Button color='success' isLight className='w-100 mt-2'>
									<Icon icon='AutoFixHigh' /> Auto-Optimize All
								</Button>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default RouteOptimization;

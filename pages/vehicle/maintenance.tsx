import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Badge from '../../components/bootstrap/Badge';

const VehicleMaintenance: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample maintenance data
	const maintenanceData = [
		{
			id: 'MAINT001',
			vehicleId: 'VH001',
			vehicleNumber: 'MH-12-AB-1234',
			type: 'Scheduled Service',
			description: 'Regular 10,000 km service',
			status: 'Completed',
			scheduledDate: '2024-01-15',
			completedDate: '2024-01-15',
			cost: '₹8,500',
			serviceCenter: 'Aadhan Service Center',
			technician: 'Ramesh Mechanic',
			nextService: '2024-04-15',
		},
		{
			id: 'MAINT002',
			vehicleId: 'VH002',
			vehicleNumber: 'MH-12-CD-5678',
			type: 'Repair',
			description: 'Brake pad replacement',
			status: 'In Progress',
			scheduledDate: '2024-01-25',
			completedDate: null,
			cost: '₹3,200',
			serviceCenter: 'Metro Auto Service',
			technician: 'Sunil Kumar',
			nextService: '2024-03-25',
		},
		{
			id: 'MAINT003',
			vehicleId: 'VH003',
			vehicleNumber: 'MH-12-EF-9012',
			type: 'Emergency Repair',
			description: 'Engine overheating issue',
			status: 'Pending',
			scheduledDate: '2024-01-28',
			completedDate: null,
			cost: '₹12,000',
			serviceCenter: 'Quick Fix Garage',
			technician: 'Pending Assignment',
			nextService: 'TBD',
		},
	];

	const upcomingMaintenance = [
		{
			vehicleNumber: 'MH-12-GH-3456',
			type: 'Scheduled Service',
			dueDate: '2024-02-05',
			daysLeft: 8,
			priority: 'Medium',
		},
		{
			vehicleNumber: 'MH-12-IJ-7890',
			type: 'Tire Replacement',
			dueDate: '2024-02-02',
			daysLeft: 5,
			priority: 'High',
		},
		{
			vehicleNumber: 'MH-12-KL-1234',
			type: 'Oil Change',
			dueDate: '2024-02-10',
			daysLeft: 13,
			priority: 'Low',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Completed': return 'success';
			case 'In Progress': return 'info';
			case 'Pending': return 'warning';
			case 'Cancelled': return 'danger';
			default: return 'secondary';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'High': return 'danger';
			case 'Medium': return 'warning';
			case 'Low': return 'success';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Vehicle Maintenance - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Build' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Vehicle Maintenance</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Schedule Maintenance
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						Maintenance Report
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Maintenance Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Build' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>25</div>
										<div className='text-muted'>Total Maintenance</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>18</div>
										<div className='text-muted'>Completed</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Schedule' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4</div>
										<div className='text-muted'>In Progress</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Warning' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>3</div>
										<div className='text-muted'>Pending</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				<div className='row'>
					{/* Maintenance History */}
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Build' /> Maintenance History
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Maintenance ID</th>
												<th>Vehicle</th>
												<th>Type</th>
												<th>Description</th>
												<th>Status</th>
												<th>Scheduled Date</th>
												<th>Cost</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{maintenanceData.map((maintenance) => (
												<tr key={maintenance.id}>
													<td className='fw-bold'>{maintenance.id}</td>
													<td>{maintenance.vehicleNumber}</td>
													<td>{maintenance.type}</td>
													<td>{maintenance.description}</td>
													<td>
														<Badge color={getStatusColor(maintenance.status)} isLight>
															{maintenance.status}
														</Badge>
													</td>
													<td>{maintenance.scheduledDate}</td>
													<td>{maintenance.cost}</td>
													<td>
														<Button
															icon='Visibility'
															color='info'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Edit'
															color='primary'
															isLight
															size='sm'>
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>

					{/* Upcoming Maintenance */}
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Schedule' /> Upcoming Maintenance
								</CardTitle>
							</CardHeader>
							<CardBody>
								{upcomingMaintenance.map((item, index) => (
									<div key={index} className='border-bottom pb-3 mb-3'>
										<div className='d-flex justify-content-between align-items-start'>
											<div className='flex-grow-1'>
												<h6 className='mb-1'>{item.vehicleNumber}</h6>
												<p className='text-muted mb-1'>{item.type}</p>
												<small className='text-muted'>Due: {item.dueDate}</small>
											</div>
											<div className='text-end'>
												<Badge color={getPriorityColor(item.priority)} isLight className='mb-1'>
													{item.priority}
												</Badge>
												<div className='small text-muted'>{item.daysLeft} days left</div>
											</div>
										</div>
									</div>
								))}
								<Button color='primary' isLight className='w-100 mt-2'>
									<Icon icon='Add' /> Schedule New
								</Button>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default VehicleMaintenance;

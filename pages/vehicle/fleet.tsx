import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const FleetManagement: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample fleet data
	const fleetData = [
		{
			id: 'VH001',
			vehicleNumber: 'MH-12-AB-1234',
			type: 'Milk Tanker',
			capacity: '5000L',
			driver: 'Rajesh Kumar',
			status: 'Active',
			location: 'Route A - Pune',
			fuelLevel: 85,
			lastMaintenance: '2024-01-15',
			nextMaintenance: '2024-02-15',
		},
		{
			id: 'VH002',
			vehicleNumber: 'MH-12-CD-5678',
			type: 'Delivery Van',
			capacity: '2000L',
			driver: 'Suresh Patil',
			status: 'In Transit',
			location: 'Route B - Mumbai',
			fuelLevel: 60,
			lastMaintenance: '2024-01-10',
			nextMaintenance: '2024-02-10',
		},
		{
			id: 'VH003',
			vehicleNumber: 'MH-12-EF-9012',
			type: 'Refrigerated Truck',
			capacity: '3000L',
			driver: 'Amit Sharma',
			status: 'Maintenance',
			location: 'Service Center',
			fuelLevel: 45,
			lastMaintenance: '2024-01-20',
			nextMaintenance: '2024-02-20',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'In Transit': return 'info';
			case 'Maintenance': return 'warning';
			case 'Inactive': return 'danger';
			default: return 'secondary';
		}
	};

	const getFuelLevelColor = (level: number) => {
		if (level > 70) return 'success';
		if (level > 30) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Fleet Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='DirectionsCar' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Fleet Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Vehicle
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						Fleet Report
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Fleet Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='LocalShipping' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>12</div>
										<div className='text-muted'>Total Vehicles</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>8</div>
										<div className='text-muted'>Active Vehicles</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Build' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2</div>
										<div className='text-muted'>In Maintenance</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Route' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>6</div>
										<div className='text-muted'>On Route</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Fleet Details Table */}
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='DirectionsCar' /> Fleet Details
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Vehicle ID</th>
												<th>Vehicle Number</th>
												<th>Type</th>
												<th>Capacity</th>
												<th>Driver</th>
												<th>Status</th>
												<th>Location</th>
												<th>Fuel Level</th>
												<th>Next Maintenance</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{fleetData.map((vehicle) => (
												<tr key={vehicle.id}>
													<td className='fw-bold'>{vehicle.id}</td>
													<td>{vehicle.vehicleNumber}</td>
													<td>{vehicle.type}</td>
													<td>{vehicle.capacity}</td>
													<td>{vehicle.driver}</td>
													<td>
														<Badge color={getStatusColor(vehicle.status)} isLight>
															{vehicle.status}
														</Badge>
													</td>
													<td>{vehicle.location}</td>
													<td>
														<div className='d-flex align-items-center'>
															<Progress 
																value={vehicle.fuelLevel} 
																color={getFuelLevelColor(vehicle.fuelLevel)}
																height={8}
																className='flex-grow-1 me-2'
															/>
															<span className='small'>{vehicle.fuelLevel}%</span>
														</div>
													</td>
													<td>{vehicle.nextMaintenance}</td>
													<td>
														<Button
															icon='Visibility'
															color='info'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Edit'
															color='primary'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Build'
															color='warning'
															isLight
															size='sm'>
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default FleetManagement;

import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const VehicleDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample vehicle data
	const vehicles = [
		{
			id: 'VH-001',
			registrationNo: 'MH-12-AB-1234',
			type: 'Refrigerated Truck',
			driver: '<PERSON><PERSON>',
			status: 'On Route',
			location: 'Mumbai to Pune',
			fuelLevel: 75,
			maintenanceScore: 92,
			lastService: '2024-01-10',
			nextService: '2024-02-10',
			mileage: 45000,
		},
		{
			id: 'VH-002',
			registrationNo: 'MH-14-CD-5678',
			type: 'Delivery Van',
			driver: 'Suresh Patel',
			status: 'Available',
			location: 'Depot - Mumbai',
			fuelLevel: 60,
			maintenanceScore: 88,
			lastService: '2024-01-05',
			nextService: '2024-02-05',
			mileage: 32000,
		},
		{
			id: 'VH-003',
			registrationNo: 'DL-08-EF-9012',
			type: 'Refrigerated Truck',
			driver: 'Vikash Singh',
			status: 'In Maintenance',
			location: 'Service Center',
			fuelLevel: 25,
			maintenanceScore: 65,
			lastService: '2024-01-15',
			nextService: '2024-01-20',
			mileage: 67000,
		},
		{
			id: 'VH-004',
			registrationNo: 'KA-05-GH-3456',
			type: 'Delivery Van',
			driver: 'Arjun Reddy',
			status: 'On Route',
			location: 'Bangalore to Chennai',
			fuelLevel: 85,
			maintenanceScore: 95,
			lastService: '2023-12-20',
			nextService: '2024-01-20',
			mileage: 28000,
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Available': return 'success';
			case 'On Route': return 'primary';
			case 'In Maintenance': return 'warning';
			case 'Out of Service': return 'danger';
			default: return 'secondary';
		}
	};

	const getVehicleTypeColor = (type: string) => {
		switch (type) {
			case 'Refrigerated Truck': return 'info';
			case 'Delivery Van': return 'success';
			case 'Tanker': return 'warning';
			case 'Pickup': return 'primary';
			default: return 'secondary';
		}
	};

	const getFuelLevelColor = (level: number) => {
		if (level >= 70) return 'success';
		if (level >= 30) return 'warning';
		return 'danger';
	};

	const getMaintenanceColor = (score: number) => {
		if (score >= 85) return 'success';
		if (score >= 70) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Vehicle Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='LocalShipping' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Vehicle Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Vehicle
					</Button>
					<Button
						icon='Route'
						color='success'
						isLight>
						Track Routes
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Vehicle Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='LocalShipping' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>24</div>
										<div className='text-muted'>Total Vehicles</div>
										<div className='small text-info'>
											<Icon icon='DirectionsCar' className='me-1' />
											Fleet size
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>18</div>
										<div className='text-muted'>Available</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											75% operational
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Route' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4</div>
										<div className='text-muted'>On Route</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											Active deliveries
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Build' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>2</div>
										<div className='text-muted'>In Maintenance</div>
										<div className='small text-info'>
											<Icon icon='Schedule' className='me-1' />
											Service ongoing
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Vehicle Fleet Table */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='LocalShipping' /> Vehicle Fleet
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Vehicle</th>
												<th>Type</th>
												<th>Driver</th>
												<th>Status</th>
												<th>Location</th>
												<th>Fuel Level</th>
												<th>Maintenance</th>
												<th>Next Service</th>
												<th>Mileage</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{vehicles.map((vehicle) => (
												<tr key={vehicle.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='LocalShipping' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{vehicle.registrationNo}</div>
																<div className='text-muted small'>{vehicle.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getVehicleTypeColor(vehicle.type)} isLight>
															{vehicle.type}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>{vehicle.driver}</div>
													</td>
													<td>
														<Badge color={getStatusColor(vehicle.status)} isLight>
															{vehicle.status}
														</Badge>
													</td>
													<td>
														<div className='text-muted small'>{vehicle.location}</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={vehicle.fuelLevel}
																	color={getFuelLevelColor(vehicle.fuelLevel)}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{vehicle.fuelLevel}%
															</small>
														</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={vehicle.maintenanceScore}
																	color={getMaintenanceColor(vehicle.maintenanceScore)}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{vehicle.maintenanceScore}%
															</small>
														</div>
													</td>
													<td>{new Date(vehicle.nextService).toLocaleDateString()}</td>
													<td>
														<div className='fw-bold'>{vehicle.mileage.toLocaleString()}</div>
														<div className='text-muted small'>km</div>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='GpsFixed'
															className='me-2'>
															Track
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Vehicle Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/vehicle/fleet'>
											<Icon icon='LocalShipping' className='me-2' />
											Fleet
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/vehicle/tracking'>
											<Icon icon='GpsFixed' className='me-2' />
											Tracking
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/vehicle/maintenance'>
											<Icon icon='Build' className='me-2' />
											Maintenance
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/vehicle/routes'>
											<Icon icon='Route' className='me-2' />
											Routes
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/vehicle/drivers'>
											<Icon icon='Person' className='me-2' />
											Drivers
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/vehicle/fuel'>
											<Icon icon='LocalGasStation' className='me-2' />
											Fuel
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Vehicle Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Delivery completed</h6>
											<p className='timeline-text'>MH-12-AB-1234 completed Mumbai to Pune route</p>
											<small className='text-muted'>1 hour ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Maintenance scheduled</h6>
											<p className='timeline-text'>DL-08-EF-9012 scheduled for service tomorrow</p>
											<small className='text-muted'>3 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Route optimized</h6>
											<p className='timeline-text'>New delivery route created for Bangalore zone</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Fuel refilled</h6>
											<p className='timeline-text'>KA-05-GH-3456 refueled at depot</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default VehicleDashboard;

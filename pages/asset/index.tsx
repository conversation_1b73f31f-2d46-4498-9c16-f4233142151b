import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const AssetDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample asset data
	const assets = [
		{
			id: 'AST-001',
			name: 'Pasteurization Unit #1',
			category: 'Production Equipment',
			location: 'Production Floor A',
			status: 'Operational',
			condition: 'Good',
			purchaseDate: '2022-03-15',
			value: 2500000,
			depreciation: 15,
			maintenanceScore: 88,
			nextMaintenance: '2024-02-01',
			warrantyExpiry: '2025-03-15',
		},
		{
			id: 'AST-002',
			name: 'Refrigeration System #2',
			category: 'Cooling Equipment',
			location: 'Cold Storage B',
			status: 'Operational',
			condition: 'Excellent',
			purchaseDate: '2023-01-20',
			value: 1800000,
			depreciation: 8,
			maintenanceScore: 95,
			nextMaintenance: '2024-01-25',
			warrantyExpiry: '2026-01-20',
		},
		{
			id: 'AST-003',
			name: 'Packaging Machine #3',
			category: 'Packaging Equipment',
			location: 'Packaging Line C',
			status: 'Under Maintenance',
			condition: 'Fair',
			purchaseDate: '2021-08-10',
			value: 1200000,
			depreciation: 25,
			maintenanceScore: 72,
			nextMaintenance: '2024-01-18',
			warrantyExpiry: '2024-08-10',
		},
		{
			id: 'AST-004',
			name: 'Quality Testing Lab Equipment',
			category: 'Testing Equipment',
			location: 'QC Laboratory',
			status: 'Operational',
			condition: 'Good',
			purchaseDate: '2022-11-05',
			value: 800000,
			depreciation: 12,
			maintenanceScore: 85,
			nextMaintenance: '2024-02-15',
			warrantyExpiry: '2025-11-05',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Operational': return 'success';
			case 'Under Maintenance': return 'warning';
			case 'Out of Service': return 'danger';
			case 'Idle': return 'info';
			default: return 'secondary';
		}
	};

	const getConditionColor = (condition: string) => {
		switch (condition) {
			case 'Excellent': return 'success';
			case 'Good': return 'primary';
			case 'Fair': return 'warning';
			case 'Poor': return 'danger';
			default: return 'secondary';
		}
	};

	const getCategoryColor = (category: string) => {
		switch (category) {
			case 'Production Equipment': return 'primary';
			case 'Cooling Equipment': return 'info';
			case 'Packaging Equipment': return 'success';
			case 'Testing Equipment': return 'warning';
			default: return 'secondary';
		}
	};

	const getMaintenanceColor = (score: number) => {
		if (score >= 85) return 'success';
		if (score >= 70) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Asset Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Inventory' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Asset Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Asset
					</Button>
					<Button
						icon='Build'
						color='success'
						isLight>
						Schedule Maintenance
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Asset Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Inventory' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>156</div>
										<div className='text-muted'>Total Assets</div>
										<div className='small text-info'>
											<Icon icon='TrendingUp' className='me-1' />
											₹8.5Cr value
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>142</div>
										<div className='text-muted'>Operational</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											91% uptime
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Build' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>8</div>
										<div className='text-muted'>Under Maintenance</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											Scheduled service
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='TrendingDown' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>18%</div>
										<div className='text-muted'>Avg Depreciation</div>
										<div className='small text-info'>
											<Icon icon='Schedule' className='me-1' />
											Annual rate
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Asset Inventory Table */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Inventory' /> Asset Inventory
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Asset</th>
												<th>Category</th>
												<th>Location</th>
												<th>Status</th>
												<th>Condition</th>
												<th>Value</th>
												<th>Depreciation</th>
												<th>Maintenance</th>
												<th>Next Service</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{assets.map((asset) => (
												<tr key={asset.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Inventory' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{asset.name}</div>
																<div className='text-muted small'>{asset.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getCategoryColor(asset.category)} isLight>
															{asset.category}
														</Badge>
													</td>
													<td>
														<div className='text-muted small'>{asset.location}</div>
													</td>
													<td>
														<Badge color={getStatusColor(asset.status)} isLight>
															{asset.status}
														</Badge>
													</td>
													<td>
														<Badge color={getConditionColor(asset.condition)} isLight>
															{asset.condition}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>₹{(asset.value / 100000).toFixed(1)}L</div>
														<div className='text-muted small'>Purchase value</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={asset.depreciation}
																	color={asset.depreciation <= 15 ? 'success' : asset.depreciation <= 30 ? 'warning' : 'danger'}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{asset.depreciation}%
															</small>
														</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={asset.maintenanceScore}
																	color={getMaintenanceColor(asset.maintenanceScore)}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{asset.maintenanceScore}%
															</small>
														</div>
													</td>
													<td>{new Date(asset.nextMaintenance).toLocaleDateString()}</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Asset Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/asset/inventory'>
											<Icon icon='Inventory' className='me-2' />
											Inventory
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/asset/maintenance'>
											<Icon icon='Build' className='me-2' />
											Maintenance
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/asset/depreciation'>
											<Icon icon='TrendingDown' className='me-2' />
											Depreciation
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/asset/facilities'>
											<Icon icon='Business' className='me-2' />
											Facilities
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/asset/warranty'>
											<Icon icon='VerifiedUser' className='me-2' />
											Warranty
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/asset/reports'>
											<Icon icon='Assessment' className='me-2' />
											Reports
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Asset Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Maintenance completed</h6>
											<p className='timeline-text'>Pasteurization Unit #1 service completed</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Maintenance scheduled</h6>
											<p className='timeline-text'>Packaging Machine #3 scheduled for repair</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Asset registered</h6>
											<p className='timeline-text'>New refrigeration unit added to inventory</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Warranty renewed</h6>
											<p className='timeline-text'>Quality Testing Lab Equipment warranty extended</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default AssetDashboard;

import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const RolesDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample roles data
	const roles = [
		{
			id: 'ROLE-001',
			name: 'Administrator',
			description: 'Full system access and administrative privileges',
			users: 3,
			permissions: 45,
			status: 'Active',
			level: 'System',
			createdDate: '2023-01-15',
			lastModified: '2024-01-10',
		},
		{
			id: 'ROLE-002',
			name: 'Production Manager',
			description: 'Manage production operations and quality control',
			users: 8,
			permissions: 28,
			status: 'Active',
			level: 'Department',
			createdDate: '2023-02-20',
			lastModified: '2024-01-08',
		},
		{
			id: 'ROLE-003',
			name: 'Sales Executive',
			description: 'Handle sales operations and customer management',
			users: 15,
			permissions: 18,
			status: 'Active',
			level: 'Department',
			createdDate: '2023-03-10',
			lastModified: '2024-01-05',
		},
		{
			id: 'ROLE-004',
			name: 'Quality Inspector',
			description: 'Quality control and FSSAI compliance monitoring',
			users: 6,
			permissions: 12,
			status: 'Active',
			level: 'Operational',
			createdDate: '2023-04-05',
			lastModified: '2023-12-20',
		},
		{
			id: 'ROLE-005',
			name: 'Warehouse Staff',
			description: 'Inventory management and warehouse operations',
			users: 12,
			permissions: 8,
			status: 'Active',
			level: 'Operational',
			createdDate: '2023-05-15',
			lastModified: '2023-12-15',
		},
		{
			id: 'ROLE-006',
			name: 'Guest User',
			description: 'Limited read-only access for external users',
			users: 5,
			permissions: 3,
			status: 'Inactive',
			level: 'Guest',
			createdDate: '2023-06-01',
			lastModified: '2023-11-30',
		},
	];

	const permissions = [
		{
			module: 'Procurement',
			read: true,
			write: true,
			delete: false,
			admin: true,
		},
		{
			module: 'Sales & Marketing',
			read: true,
			write: true,
			delete: true,
			admin: false,
		},
		{
			module: 'Operations',
			read: true,
			write: false,
			delete: false,
			admin: false,
		},
		{
			module: 'FSSAI',
			read: true,
			write: true,
			delete: false,
			admin: true,
		},
		{
			module: 'Inventory',
			read: true,
			write: true,
			delete: false,
			admin: false,
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'Inactive': return 'danger';
			case 'Pending': return 'warning';
			default: return 'secondary';
		}
	};

	const getLevelColor = (level: string) => {
		switch (level) {
			case 'System': return 'danger';
			case 'Department': return 'primary';
			case 'Operational': return 'success';
			case 'Guest': return 'secondary';
			default: return 'info';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Roles & Permissions - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='AdminPanelSettings' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Roles & Permissions</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Create Role
					</Button>
					<Button
						icon='Security'
						color='success'
						isLight>
						Audit Permissions
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Roles Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='AdminPanelSettings' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>12</div>
										<div className='text-muted'>Total Roles</div>
										<div className='small text-info'>
											<Icon icon='Group' className='me-1' />
											6 active roles
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>156</div>
										<div className='text-muted'>Total Users</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											All assigned roles
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Security' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>45</div>
										<div className='text-muted'>Permissions</div>
										<div className='small text-warning'>
											<Icon icon='Shield' className='me-1' />
											Across all modules
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='VerifiedUser' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>98%</div>
										<div className='text-muted'>Compliance Score</div>
										<div className='small text-success'>
											<Icon icon='CheckCircle' className='me-1' />
											Security compliant
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Roles Table */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='AdminPanelSettings' /> System Roles
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Role</th>
												<th>Level</th>
												<th>Users</th>
												<th>Permissions</th>
												<th>Status</th>
												<th>Last Modified</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{roles.map((role) => (
												<tr key={role.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='AdminPanelSettings' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{role.name}</div>
																<div className='text-muted small'>{role.id}</div>
																<div className='text-muted small'>{role.description}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getLevelColor(role.level)} isLight>
															{role.level}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>{role.users}</div>
														<div className='text-muted small'>assigned users</div>
													</td>
													<td>
														<div className='fw-bold'>{role.permissions}</div>
														<div className='text-muted small'>permissions</div>
													</td>
													<td>
														<Badge color={getStatusColor(role.status)} isLight>
															{role.status}
														</Badge>
													</td>
													<td>{new Date(role.lastModified).toLocaleDateString()}</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Permission Matrix */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Role Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/roles/manage'>
											<Icon icon='AdminPanelSettings' className='me-2' />
											Manage Roles
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/roles/permissions'>
											<Icon icon='Security' className='me-2' />
											Permissions
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/roles/users'>
											<Icon icon='People' className='me-2' />
											User Assignment
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/roles/audit'>
											<Icon icon='Assessment' className='me-2' />
											Audit Trail
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/roles/templates'>
											<Icon icon='FileCopy' className='me-2' />
											Role Templates
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/roles/reports'>
											<Icon icon='Description' className='me-2' />
											Reports
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Permission Matrix (Sample Role)</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-sm'>
										<thead>
											<tr>
												<th>Module</th>
												<th className='text-center'>Read</th>
												<th className='text-center'>Write</th>
												<th className='text-center'>Delete</th>
												<th className='text-center'>Admin</th>
											</tr>
										</thead>
										<tbody>
											{permissions.map((perm, index) => (
												<tr key={index}>
													<td className='fw-bold'>{perm.module}</td>
													<td className='text-center'>
														{perm.read ? (
															<Icon icon='CheckCircle' color='success' />
														) : (
															<Icon icon='Cancel' color='danger' />
														)}
													</td>
													<td className='text-center'>
														{perm.write ? (
															<Icon icon='CheckCircle' color='success' />
														) : (
															<Icon icon='Cancel' color='danger' />
														)}
													</td>
													<td className='text-center'>
														{perm.delete ? (
															<Icon icon='CheckCircle' color='success' />
														) : (
															<Icon icon='Cancel' color='danger' />
														)}
													</td>
													<td className='text-center'>
														{perm.admin ? (
															<Icon icon='CheckCircle' color='success' />
														) : (
															<Icon icon='Cancel' color='danger' />
														)}
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
								<div className='mt-3'>
									<Button
										color='primary'
										isLight
										className='w-100'
										icon='Edit'>
										Edit Permissions
									</Button>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default RolesDashboard;

import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const PISDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample product data
	const products = [
		{
			id: 'PRD-001',
			name: 'Whole Milk',
			category: 'Dairy Products',
			sku: 'WM-1L-001',
			status: 'Active',
			batchesProduced: 45,
			currentStock: 2500,
			shelfLife: '7 days',
			nutritionalScore: 95,
			qualityGrade: 'A+',
		},
		{
			id: 'PRD-002',
			name: 'Greek Yogurt',
			category: 'Dairy Products',
			sku: 'GY-500G-002',
			status: 'Active',
			batchesProduced: 32,
			currentStock: 800,
			shelfLife: '14 days',
			nutritionalScore: 92,
			qualityGrade: 'A',
		},
		{
			id: 'PRD-003',
			name: 'Cheddar Cheese',
			category: 'Cheese Products',
			sku: 'CC-200G-003',
			status: 'Active',
			batchesProduced: 18,
			currentStock: 150,
			shelfLife: '30 days',
			nutritionalScore: 88,
			qualityGrade: 'A',
		},
		{
			id: 'PRD-004',
			name: 'Butter',
			category: 'Dairy Products',
			sku: 'BT-250G-004',
			status: 'Development',
			batchesProduced: 5,
			currentStock: 50,
			shelfLife: '45 days',
			nutritionalScore: 85,
			qualityGrade: 'B+',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'Development': return 'warning';
			case 'Discontinued': return 'danger';
			case 'Testing': return 'info';
			default: return 'secondary';
		}
	};

	const getCategoryColor = (category: string) => {
		switch (category) {
			case 'Dairy Products': return 'primary';
			case 'Cheese Products': return 'success';
			case 'Beverages': return 'info';
			case 'Desserts': return 'warning';
			default: return 'secondary';
		}
	};

	const getGradeColor = (grade: string) => {
		if (grade.startsWith('A')) return 'success';
		if (grade.startsWith('B')) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Product Information System - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Inventory2' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Product Information System</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Add Product
					</Button>
					<Button
						icon='Science'
						color='success'
						isLight>
						Quality Test
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Product Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Inventory2' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>24</div>
										<div className='text-muted'>Total Products</div>
										<div className='small text-info'>
											<Icon icon='Category' className='me-1' />
											4 categories
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>18</div>
										<div className='text-muted'>Active Products</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											75% of portfolio
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Science' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4</div>
										<div className='text-muted'>In Development</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											Testing phase
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Star' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>91%</div>
										<div className='text-muted'>Avg Quality Score</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Above target
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Product Catalog */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Inventory2' /> Product Catalog
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Product</th>
												<th>Category</th>
												<th>SKU</th>
												<th>Status</th>
												<th>Batches</th>
												<th>Stock</th>
												<th>Shelf Life</th>
												<th>Quality</th>
												<th>Grade</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{products.map((product) => (
												<tr key={product.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Inventory2' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{product.name}</div>
																<div className='text-muted small'>{product.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getCategoryColor(product.category)} isLight>
															{product.category}
														</Badge>
													</td>
													<td>
														<code className='text-muted'>{product.sku}</code>
													</td>
													<td>
														<Badge color={getStatusColor(product.status)} isLight>
															{product.status}
														</Badge>
													</td>
													<td>
														<div className='fw-bold'>{product.batchesProduced}</div>
														<div className='text-muted small'>batches</div>
													</td>
													<td>
														<div className='fw-bold'>{product.currentStock.toLocaleString()}</div>
														<div className='text-muted small'>units</div>
													</td>
													<td>{product.shelfLife}</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={product.nutritionalScore}
																	color={product.nutritionalScore >= 90 ? 'success' : product.nutritionalScore >= 80 ? 'warning' : 'danger'}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{product.nutritionalScore}%
															</small>
														</div>
													</td>
													<td>
														<Badge color={getGradeColor(product.qualityGrade)} isLight>
															{product.qualityGrade}
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Product Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/pis/catalog'>
											<Icon icon='Inventory2' className='me-2' />
											Catalog
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/pis/specifications'>
											<Icon icon='Description' className='me-2' />
											Specifications
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/pis/nutrition'>
											<Icon icon='LocalDining' className='me-2' />
											Nutrition
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/pis/lifecycle'>
											<Icon icon='Timeline' className='me-2' />
											Lifecycle
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/pis/batches'>
											<Icon icon='Layers' className='me-2' />
											Batches
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/pis/quality'>
											<Icon icon='VerifiedUser' className='me-2' />
											Quality
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Product Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New product approved</h6>
											<p className='timeline-text'>Greek Yogurt 500g passed quality testing</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Batch quality review</h6>
											<p className='timeline-text'>Cheddar Cheese batch #CC-2024-001 under review</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Nutritional analysis updated</h6>
											<p className='timeline-text'>Whole Milk nutritional profile updated</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New product development</h6>
											<p className='timeline-text'>Butter 250g entered development phase</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default PISDashboard;

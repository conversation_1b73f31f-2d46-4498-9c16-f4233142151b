import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const IssueTrackerDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample issue data
	const issues = [
		{
			id: 'ISS-001',
			title: 'Pasteurization Unit Temperature Issue',
			description: 'Temperature not maintaining optimal range',
			reporter: '<PERSON>esh Kumar',
			assignee: 'Maintenance Team',
			priority: 'High',
			status: 'In Progress',
			category: 'Equipment',
			createdDate: '2024-01-15',
			dueDate: '2024-01-18',
			progress: 65,
		},
		{
			id: 'ISS-002',
			title: 'Quality Control Lab Software Bug',
			description: 'Test result calculation showing incorrect values',
			reporter: 'Priya Sharma',
			assignee: 'IT Support',
			priority: 'Medium',
			status: 'Open',
			category: 'Software',
			createdDate: '2024-01-14',
			dueDate: '2024-01-20',
			progress: 25,
		},
		{
			id: 'ISS-003',
			title: 'Delivery Vehicle Breakdown',
			description: 'Refrigeration system failure in vehicle MH-12-AB-1234',
			reporter: 'Ramesh Kumar',
			assignee: 'Vehicle Team',
			priority: 'Critical',
			status: 'Resolved',
			category: 'Vehicle',
			createdDate: '2024-01-13',
			dueDate: '2024-01-15',
			progress: 100,
		},
		{
			id: 'ISS-004',
			title: 'Employee Access Card Not Working',
			description: 'Unable to access production area with ID card',
			reporter: 'Amit Singh',
			assignee: 'Security Team',
			priority: 'Low',
			status: 'Open',
			category: 'Security',
			createdDate: '2024-01-12',
			dueDate: '2024-01-19',
			progress: 10,
		},
	];

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'Critical': return 'danger';
			case 'High': return 'warning';
			case 'Medium': return 'info';
			case 'Low': return 'success';
			default: return 'secondary';
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Open': return 'info';
			case 'In Progress': return 'warning';
			case 'Resolved': return 'success';
			case 'Closed': return 'secondary';
			case 'Rejected': return 'danger';
			default: return 'secondary';
		}
	};

	const getCategoryColor = (category: string) => {
		switch (category) {
			case 'Equipment': return 'primary';
			case 'Software': return 'info';
			case 'Vehicle': return 'warning';
			case 'Security': return 'danger';
			case 'Process': return 'success';
			default: return 'secondary';
		}
	};

	const getProgressColor = (progress: number) => {
		if (progress >= 80) return 'success';
		if (progress >= 50) return 'warning';
		if (progress >= 25) return 'info';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Issue Tracker - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='BugReport' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Issue Tracker</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						Report Issue
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						Analytics
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Issue Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='BugReport' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>47</div>
										<div className='text-muted'>Total Issues</div>
										<div className='small text-info'>
											<Icon icon='TrendingUp' className='me-1' />
											This month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Schedule' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>12</div>
										<div className='text-muted'>Open Issues</div>
										<div className='small text-warning'>
											<Icon icon='Warning' className='me-1' />
											Needs attention
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Autorenew' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>8</div>
										<div className='text-muted'>In Progress</div>
										<div className='small text-info'>
											<Icon icon='Build' className='me-1' />
											Being resolved
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>27</div>
										<div className='text-muted'>Resolved</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											57% resolution rate
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Issues Table */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='BugReport' /> Issue Tickets
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Issue</th>
												<th>Reporter</th>
												<th>Assignee</th>
												<th>Priority</th>
												<th>Status</th>
												<th>Category</th>
												<th>Progress</th>
												<th>Due Date</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{issues.map((issue) => (
												<tr key={issue.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='BugReport' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{issue.title}</div>
																<div className='text-muted small'>{issue.id}</div>
																<div className='text-muted small'>{issue.description}</div>
															</div>
														</div>
													</td>
													<td>
														<div className='fw-bold'>{issue.reporter}</div>
													</td>
													<td>
														<div className='fw-bold'>{issue.assignee}</div>
													</td>
													<td>
														<Badge color={getPriorityColor(issue.priority)} isLight>
															{issue.priority}
														</Badge>
													</td>
													<td>
														<Badge color={getStatusColor(issue.status)} isLight>
															{issue.status}
														</Badge>
													</td>
													<td>
														<Badge color={getCategoryColor(issue.category)} isLight>
															{issue.category}
														</Badge>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={issue.progress}
																	color={getProgressColor(issue.progress)}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{issue.progress}%
															</small>
														</div>
													</td>
													<td>{new Date(issue.dueDate).toLocaleDateString()}</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Issue Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/issue-tracker/tickets'>
											<Icon icon='BugReport' className='me-2' />
											Tickets
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/issue-tracker/reports'>
											<Icon icon='Assessment' className='me-2' />
											Reports
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/issue-tracker/categories'>
											<Icon icon='Category' className='me-2' />
											Categories
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/issue-tracker/assignments'>
											<Icon icon='Assignment' className='me-2' />
											Assignments
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/issue-tracker/knowledge-base'>
											<Icon icon='MenuBook' className='me-2' />
											Knowledge Base
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/issue-tracker/settings'>
											<Icon icon='Settings' className='me-2' />
											Settings
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Issue Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Issue resolved</h6>
											<p className='timeline-text'>Delivery Vehicle Breakdown - ISS-003 marked as resolved</p>
											<small className='text-muted'>1 hour ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Issue assigned</h6>
											<p className='timeline-text'>Pasteurization Unit Issue assigned to Maintenance Team</p>
											<small className='text-muted'>3 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New issue reported</h6>
											<p className='timeline-text'>Quality Control Lab Software Bug - ISS-002</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Issue escalated</h6>
											<p className='timeline-text'>Employee Access Card issue escalated to Security Team</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default IssueTrackerDashboard;

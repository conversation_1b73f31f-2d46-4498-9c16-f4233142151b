import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../layout/SubHeader/SubHeader';
import Page from '../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../components/bootstrap/Card';
import Button from '../components/bootstrap/Button';
import Icon from '../components/icon/Icon';
import Badge from '../components/bootstrap/Badge';
import Progress from '../components/bootstrap/Progress';

const Index: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample dashboard data for Aadhan Dairy
	const todayStats = {
		milkProduction: 15420,
		salesRevenue: 2850000,
		activeOrders: 156,
		qualityScore: 98.5,
	};

	const recentActivities = [
		{
			time: '10:30 AM',
			activity: 'Quality check completed for Batch #2024-001',
			status: 'success',
		},
		{
			time: '09:45 AM',
			activity: 'New purchase order received from Metro Stores',
			status: 'info',
		},
		{
			time: '09:15 AM',
			activity: 'Production line 2 maintenance scheduled',
			status: 'warning',
		},
		{
			time: '08:30 AM',
			activity: 'Daily inventory count completed',
			status: 'success',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'success': return 'success';
			case 'warning': return 'warning';
			case 'info': return 'info';
			case 'danger': return 'danger';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Aadhan Dairy ERP - Dashboard</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Dashboard' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Aadhan Dairy Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Refresh'
						color='primary'
						isLight
						className='me-2'>
						Refresh Data
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						Generate Report
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Key Metrics Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='LocalDrink' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>{todayStats.milkProduction.toLocaleString()}</div>
										<div className='text-muted'>Liters Produced Today</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+5.2% from yesterday
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='AttachMoney' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹{(todayStats.salesRevenue / 100000).toFixed(1)}L</div>
										<div className='text-muted'>Sales Revenue Today</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+8.1% from yesterday
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='ShoppingCart' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>{todayStats.activeOrders}</div>
										<div className='text-muted'>Active Orders</div>
										<div className='small text-info'>
											<Icon icon='Schedule' className='me-1' />
											12 pending delivery
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='VerifiedUser' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>{todayStats.qualityScore}%</div>
										<div className='text-muted'>Quality Score</div>
										<div className='small text-success'>
											<Icon icon='CheckCircle' className='me-1' />
											FSSAI Compliant
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Dashboard' /> Quick Actions
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-md-4 col-sm-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/procurement'>
											<Icon icon='Inventory' className='me-2' />
											Procurement
										</Button>
									</div>
									<div className='col-md-4 col-sm-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales'>
											<Icon icon='TrendingUp' className='me-2' />
											Sales
										</Button>
									</div>
									<div className='col-md-4 col-sm-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/operations'>
											<Icon icon='Precision Manufacturing' className='me-2' />
											Operations
										</Button>
									</div>
									<div className='col-md-4 col-sm-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/inventory'>
											<Icon icon='Warehouse' className='me-2' />
											Inventory
										</Button>
									</div>
									<div className='col-md-4 col-sm-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/fssai'>
											<Icon icon='VerifiedUser' className='me-2' />
											FSSAI
										</Button>
									</div>
									<div className='col-md-4 col-sm-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/crm'>
											<Icon icon='People' className='me-2' />
											CRM
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									{recentActivities.map((activity, index) => (
										<div key={index} className='timeline-item'>
											<div className={`timeline-marker bg-${getStatusColor(activity.status)}`}></div>
											<div className='timeline-content'>
												<h6 className='timeline-title'>{activity.time}</h6>
												<p className='timeline-text'>{activity.activity}</p>
												<Badge color={getStatusColor(activity.status)} isLight>
													{activity.status}
												</Badge>
											</div>
										</div>
									))}
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* System Status */}
				<div className='row'>
					<div className='col-12'>
						<Card>
							<CardHeader>
								<CardTitle>System Status</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-4'>
									<div className='col-lg-3 col-md-6'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Production Line 1</span>
											<Badge color='success' isLight>Online</Badge>
										</div>
										<Progress value={95} color='success' height={8} />
									</div>
									<div className='col-lg-3 col-md-6'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Production Line 2</span>
											<Badge color='warning' isLight>Maintenance</Badge>
										</div>
										<Progress value={0} color='warning' height={8} />
									</div>
									<div className='col-lg-3 col-md-6'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Cold Storage</span>
											<Badge color='success' isLight>Optimal</Badge>
										</div>
										<Progress value={88} color='success' height={8} />
									</div>
									<div className='col-lg-3 col-md-6'>
										<div className='d-flex justify-content-between align-items-center mb-2'>
											<span className='fw-bold'>Quality Lab</span>
											<Badge color='info' isLight>Testing</Badge>
										</div>
										<Progress value={72} color='info' height={8} />
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default Index;

import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Badge from '../../components/bootstrap/Badge';

const CustomerOrders: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample order data
	const orderData = [
		{
			id: 'ORD-2024-001',
			customer: 'Metro Stores',
			date: '2024-01-25',
			items: 'Fresh Milk 500L, Yogurt 200L',
			amount: '₹45,000',
			status: 'Confirmed',
			deliveryDate: '2024-01-26',
			priority: 'High',
			paymentStatus: 'Paid',
		},
		{
			id: 'ORD-2024-002',
			customer: 'Big Bazaar',
			date: '2024-01-25',
			items: 'Fresh Milk 300L, Buttermilk 150L',
			amount: '₹28,500',
			status: 'Processing',
			deliveryDate: '2024-01-27',
			priority: 'Medium',
			paymentStatus: 'Pending',
		},
		{
			id: 'ORD-2024-003',
			customer: 'D-Mart',
			date: '2024-01-24',
			items: 'Fresh Milk 400L, Cream 100L',
			amount: '₹38,200',
			status: 'Delivered',
			deliveryDate: '2024-01-25',
			priority: 'Medium',
			paymentStatus: 'Paid',
		},
		{
			id: 'ORD-2024-004',
			customer: 'Local Retailers',
			date: '2024-01-24',
			items: 'Mixed Dairy Products',
			amount: '₹15,800',
			status: 'Cancelled',
			deliveryDate: '2024-01-26',
			priority: 'Low',
			paymentStatus: 'Refunded',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Confirmed': return 'success';
			case 'Processing': return 'info';
			case 'Delivered': return 'primary';
			case 'Cancelled': return 'danger';
			case 'Pending': return 'warning';
			default: return 'secondary';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'High': return 'danger';
			case 'Medium': return 'warning';
			case 'Low': return 'success';
			default: return 'secondary';
		}
	};

	const getPaymentStatusColor = (status: string) => {
		switch (status) {
			case 'Paid': return 'success';
			case 'Pending': return 'warning';
			case 'Refunded': return 'info';
			case 'Overdue': return 'danger';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Customer Orders - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='ShoppingBag' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Customer Orders</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						New Order
					</Button>
					<Button
						icon='FileDownload'
						color='success'
						isLight
						className='me-2'>
						Export
					</Button>
					<Button
						icon='Assessment'
						color='info'
						isLight>
						Order Analytics
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Order Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='ShoppingCart' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>156</div>
										<div className='text-muted'>Total Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>128</div>
										<div className='text-muted'>Completed</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Schedule' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>18</div>
										<div className='text-muted'>Processing</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='AttachMoney' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹28.5L</div>
										<div className='text-muted'>Total Value</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Orders Table */}
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='ShoppingBag' /> Recent Customer Orders
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Order ID</th>
												<th>Customer</th>
												<th>Order Date</th>
												<th>Items</th>
												<th>Amount</th>
												<th>Status</th>
												<th>Priority</th>
												<th>Delivery Date</th>
												<th>Payment</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{orderData.map((order) => (
												<tr key={order.id}>
													<td className='fw-bold'>{order.id}</td>
													<td>{order.customer}</td>
													<td>{order.date}</td>
													<td>{order.items}</td>
													<td className='fw-bold'>{order.amount}</td>
													<td>
														<Badge color={getStatusColor(order.status)} isLight>
															{order.status}
														</Badge>
													</td>
													<td>
														<Badge color={getPriorityColor(order.priority)} isLight>
															{order.priority}
														</Badge>
													</td>
													<td>{order.deliveryDate}</td>
													<td>
														<Badge color={getPaymentStatusColor(order.paymentStatus)} isLight>
															{order.paymentStatus}
														</Badge>
													</td>
													<td>
														<Button
															icon='Visibility'
															color='info'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Edit'
															color='primary'
															isLight
															size='sm'
															className='me-1'>
														</Button>
														<Button
															icon='Print'
															color='success'
															isLight
															size='sm'>
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions */}
				<div className='row mt-4'>
					<div className='col-12'>
						<Card>
							<CardHeader>
								<CardTitle>Quick Actions</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row'>
									<div className='col-md-3'>
										<Button color='primary' isLight className='w-100 mb-2'>
											<Icon icon='Add' /> Create Bulk Order
										</Button>
									</div>
									<div className='col-md-3'>
										<Button color='success' isLight className='w-100 mb-2'>
											<Icon icon='LocalShipping' /> Schedule Delivery
										</Button>
									</div>
									<div className='col-md-3'>
										<Button color='info' isLight className='w-100 mb-2'>
											<Icon icon='Payment' /> Process Payments
										</Button>
									</div>
									<div className='col-md-3'>
										<Button color='warning' isLight className='w-100 mb-2'>
											<Icon icon='Notifications' /> Send Reminders
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default CustomerOrders;

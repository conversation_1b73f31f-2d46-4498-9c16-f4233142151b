import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';

const SalesAnalytics: NextPage = () => {
	return (
		<PageWrapper>
			<Head>
				<title>Sales Analytics - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Analytics' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Sales Analytics</span>
				</SubHeaderLeft>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Analytics' /> Sales Performance Dashboard
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row'>
									<div className='col-md-6'>
										<h6>Monthly Sales Trends</h6>
										<p className='text-muted'>Track sales performance over time with detailed analytics and insights.</p>
									</div>
									<div className='col-md-6'>
										<h6>Product Performance</h6>
										<p className='text-muted'>Analyze which dairy products are performing best in the market.</p>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default SalesAnalytics;

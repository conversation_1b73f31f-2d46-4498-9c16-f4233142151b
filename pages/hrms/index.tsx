import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';
import Chart, { IChartOptions } from '../../components/extras/Chart';

const HRMSDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample employee data
	const employees = [
		{
			id: 'EMP-001',
			name: '<PERSON>esh <PERSON>',
			department: 'Production',
			position: 'Production Manager',
			email: '<EMAIL>',
			phone: '+91 98765 43210',
			joinDate: '2022-03-15',
			status: 'Active',
			attendance: 95,
			performance: 88,
		},
		{
			id: 'EMP-002',
			name: 'Priya Sharma',
			department: 'Quality Control',
			position: 'QC Supervisor',
			email: '<EMAIL>',
			phone: '+91 87654 32109',
			joinDate: '2021-08-20',
			status: 'Active',
			attendance: 92,
			performance: 91,
		},
		{
			id: 'EMP-003',
			name: 'Amit Singh',
			department: 'Sales',
			position: 'Sales Executive',
			email: '<EMAIL>',
			phone: '+91 76543 21098',
			joinDate: '2023-01-10',
			status: 'Active',
			attendance: 88,
			performance: 85,
		},
		{
			id: 'EMP-004',
			name: 'Sunita Patel',
			department: 'Administration',
			position: 'HR Executive',
			email: '<EMAIL>',
			phone: '+91 65432 10987',
			joinDate: '2020-11-05',
			status: 'On Leave',
			attendance: 90,
			performance: 89,
		},
	];

	// Chart data for department distribution
	const departmentData: IChartOptions = {
		series: [35, 28, 22, 15],
		options: {
			chart: {
				type: 'donut',
				height: 300,
			},
			labels: ['Production', 'Quality Control', 'Sales & Marketing', 'Administration'],
			colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
			legend: {
				position: 'bottom',
			},
		},
	};

	const getDepartmentColor = (department: string) => {
		switch (department) {
			case 'Production': return 'primary';
			case 'Quality Control': return 'success';
			case 'Sales': return 'warning';
			case 'Administration': return 'info';
			default: return 'secondary';
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'On Leave': return 'warning';
			case 'Inactive': return 'danger';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Human Resource Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='People' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Human Resource Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='PersonAdd'
						color='primary'
						isLight
						className='me-2'>
						Add Employee
					</Button>
					<Button
						icon='Assessment'
						color='success'
						isLight>
						HR Reports
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* HR Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>142</div>
										<div className='text-muted'>Total Employees</div>
										<div className='small text-info'>
											<Icon icon='TrendingUp' className='me-1' />
											+8 this month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>91%</div>
										<div className='text-muted'>Attendance Rate</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Above target
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Schedule' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>5</div>
										<div className='text-muted'>On Leave Today</div>
										<div className='small text-warning'>
											<Icon icon='Info' className='me-1' />
											3.5% of workforce
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Star' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>4.3</div>
										<div className='text-muted'>Avg Performance</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Excellent rating
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Employee List and Department Chart */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='People' /> Employee Directory
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Employee</th>
												<th>Department</th>
												<th>Position</th>
												<th>Contact</th>
												<th>Join Date</th>
												<th>Attendance</th>
												<th>Performance</th>
												<th>Status</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{employees.map((employee) => (
												<tr key={employee.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Person' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{employee.name}</div>
																<div className='text-muted small'>{employee.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getDepartmentColor(employee.department)} isLight>
															{employee.department}
														</Badge>
													</td>
													<td>{employee.position}</td>
													<td>
														<div>
															<div className='text-muted small'>{employee.email}</div>
															<div className='text-muted small'>{employee.phone}</div>
														</div>
													</td>
													<td>{new Date(employee.joinDate).toLocaleDateString()}</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={employee.attendance}
																	color={employee.attendance >= 90 ? 'success' : employee.attendance >= 80 ? 'warning' : 'danger'}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{employee.attendance}%
															</small>
														</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={employee.performance}
																	color={employee.performance >= 85 ? 'success' : employee.performance >= 70 ? 'warning' : 'danger'}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{employee.performance}%
															</small>
														</div>
													</td>
													<td>
														<Badge color={getStatusColor(employee.status)} isLight>
															{employee.status}
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Department Distribution</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={departmentData.series}
									options={departmentData.options}
									type='donut'
									height={300}
								/>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>HR Management</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/hrms/employees'>
											<Icon icon='People' className='me-2' />
											Employees
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/hrms/attendance'>
											<Icon icon='Schedule' className='me-2' />
											Attendance
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/hrms/payroll'>
											<Icon icon='Payment' className='me-2' />
											Payroll
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/hrms/performance'>
											<Icon icon='Star' className='me-2' />
											Performance
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/hrms/leaves'>
											<Icon icon='EventBusy' className='me-2' />
											Leaves
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/hrms/training'>
											<Icon icon='School' className='me-2' />
											Training
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent HR Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New employee onboarded</h6>
											<p className='timeline-text'>Amit Singh joined Sales department</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Leave approved</h6>
											<p className='timeline-text'>Sunita Patel's medical leave approved</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Performance review completed</h6>
											<p className='timeline-text'>Q4 performance reviews for Production team</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Training session scheduled</h6>
											<p className='timeline-text'>Food safety training for all staff</p>
											<small className='text-muted'>2 days ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default HRMSDashboard;

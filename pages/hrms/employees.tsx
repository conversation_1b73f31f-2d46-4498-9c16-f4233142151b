import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft } from '../../layout/SubHeader/SubHeader';
import Icon from '../../components/icon/Icon';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';

const Employees: NextPage = () => {
	return (
		<PageWrapper>
			<Head>
				<title>Employee Records - A<PERSON>han Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Person' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Employee Records</span>
				</SubHeaderLeft>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Person' /> Employee Records
								</CardTitle>
							</CardHeader>
							<CardBody>
								<p className='text-muted'>
									Employee Records module for Aadhan Dairy ERP system. This page will contain 
									functionality related to employee records management.
								</p>
								<div className='alert alert-info'>
									<Icon icon='Info' /> This module is under development and will be enhanced with specific dairy management features.
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default Employees;
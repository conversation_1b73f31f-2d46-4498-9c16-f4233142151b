import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Modal, { ModalBody, ModalFooter, ModalHeader, ModalTitle } from '../../components/bootstrap/Modal';
import FormGroup from '../../components/bootstrap/forms/FormGroup';
import Input from '../../components/bootstrap/forms/Input';
import Label from '../../components/bootstrap/forms/Label';
import Select from '../../components/bootstrap/forms/Select';
import Option from '../../components/bootstrap/Option';

const CustomersPage: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);
	const [isModalOpen, setIsModalOpen] = useState(false);

	// Sample customers data
	const customers = [
		{
			id: 'CUST-001',
			name: 'Retail Chain ABC',
			type: 'Retailer',
			contact: 'John Smith',
			phone: '+91 98765 43210',
			email: '<EMAIL>',
			location: 'Mumbai, Maharashtra',
			totalOrders: 45,
			totalValue: 2250000,
			status: 'Active',
			lastOrder: '2024-01-15',
		},
		{
			id: 'CUST-002',
			name: 'XYZ Supermarket',
			type: 'Supermarket',
			contact: 'Sarah Johnson',
			phone: '+91 87654 32109',
			email: '<EMAIL>',
			location: 'Delhi, Delhi',
			totalOrders: 32,
			totalValue: 1680000,
			status: 'Active',
			lastOrder: '2024-01-14',
		},
		{
			id: 'CUST-003',
			name: 'Local Distributor Ltd',
			type: 'Distributor',
			contact: 'Raj Patel',
			phone: '+91 76543 21098',
			email: '<EMAIL>',
			location: 'Pune, Maharashtra',
			totalOrders: 28,
			totalValue: 1400000,
			status: 'Active',
			lastOrder: '2024-01-13',
		},
		{
			id: 'CUST-004',
			name: 'Corner Store Network',
			type: 'Retailer',
			contact: 'Priya Sharma',
			phone: '+91 65432 10987',
			email: '<EMAIL>',
			location: 'Bangalore, Karnataka',
			totalOrders: 18,
			totalValue: 720000,
			status: 'Inactive',
			lastOrder: '2023-12-20',
		},
	];

	const getCustomerTypeColor = (type: string) => {
		switch (type) {
			case 'Retailer': return 'primary';
			case 'Supermarket': return 'success';
			case 'Distributor': return 'info';
			case 'Wholesaler': return 'warning';
			default: return 'secondary';
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active': return 'success';
			case 'Inactive': return 'warning';
			case 'Suspended': return 'danger';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Customer Management - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='People' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Customer Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						onClick={() => setIsModalOpen(true)}>
						Add Customer
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Customer Statistics */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>123</div>
										<div className='text-muted'>Total Customers</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>98</div>
										<div className='text-muted'>Active Customers</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='TrendingUp' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>15</div>
										<div className='text-muted'>New This Month</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹60.5L</div>
										<div className='text-muted'>Total Revenue</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Customers Table */}
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='People' /> Customer Directory
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Customer</th>
												<th>Type</th>
												<th>Contact Info</th>
												<th>Location</th>
												<th>Orders</th>
												<th>Total Value</th>
												<th>Status</th>
												<th>Last Order</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{customers.map((customer) => (
												<tr key={customer.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Person' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{customer.name}</div>
																<div className='text-muted small'>{customer.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getCustomerTypeColor(customer.type)} isLight>
															{customer.type}
														</Badge>
													</td>
													<td>
														<div>
															<div className='fw-bold'>{customer.contact}</div>
															<div className='text-muted small'>{customer.phone}</div>
															<div className='text-muted small'>{customer.email}</div>
														</div>
													</td>
													<td>{customer.location}</td>
													<td>
														<div className='fw-bold'>{customer.totalOrders}</div>
														<div className='text-muted small'>orders</div>
													</td>
													<td>
														<div className='fw-bold'>₹{customer.totalValue.toLocaleString()}</div>
													</td>
													<td>
														<Badge color={getStatusColor(customer.status)} isLight>
															{customer.status}
														</Badge>
													</td>
													<td>{new Date(customer.lastOrder).toLocaleDateString()}</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>

			{/* Add Customer Modal */}
			<Modal isOpen={isModalOpen} setIsOpen={setIsModalOpen} size='xl'>
				<ModalHeader setIsOpen={setIsModalOpen}>
					<ModalTitle>Add New Customer</ModalTitle>
				</ModalHeader>
				<ModalBody>
					<div className='row g-4'>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='customerName'>Customer Name</Label>
								<Input id='customerName' placeholder='Enter customer name' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='customerType'>Customer Type</Label>
								<Select id='customerType'>
									<Option value=''>Select Type</Option>
									<Option value='retailer'>Retailer</Option>
									<Option value='supermarket'>Supermarket</Option>
									<Option value='distributor'>Distributor</Option>
									<Option value='wholesaler'>Wholesaler</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='contactPerson'>Contact Person</Label>
								<Input id='contactPerson' placeholder='Contact person name' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='phone'>Phone Number</Label>
								<Input id='phone' placeholder='+91 XXXXX XXXXX' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='email'>Email Address</Label>
								<Input id='email' type='email' placeholder='<EMAIL>' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='location'>Location</Label>
								<Input id='location' placeholder='City, State' />
							</FormGroup>
						</div>
						<div className='col-12'>
							<FormGroup>
								<Label htmlFor='address'>Full Address</Label>
								<Input id='address' placeholder='Complete address' />
							</FormGroup>
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color='secondary' onClick={() => setIsModalOpen(false)}>
						Cancel
					</Button>
					<Button color='primary' onClick={() => setIsModalOpen(false)}>
						Add Customer
					</Button>
				</ModalFooter>
			</Modal>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default CustomersPage;

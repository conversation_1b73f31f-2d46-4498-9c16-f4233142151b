import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Chart, { IChartOptions } from '../../components/extras/Chart';

const SalesDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sales chart data
	const salesTrendData: IChartOptions = {
		series: [
			{
				name: 'Sales Revenue',
				data: [450000, 520000, 480000, 580000, 620000, 680000],
			},
		],
		options: {
			chart: {
				type: 'area',
				height: 350,
			},
			xaxis: {
				categories: ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
			},
			yaxis: {
				labels: {
					formatter: (value) => `₹${(value / 1000).toFixed(0)}K`,
				},
			},
			fill: {
				type: 'gradient',
				gradient: {
					shadeIntensity: 1,
					opacityFrom: 0.7,
					opacityTo: 0.3,
				},
			},
			colors: ['#10b981'],
		},
	};

	const productSalesData: IChartOptions = {
		series: [35, 25, 20, 15, 5],
		options: {
			chart: {
				type: 'donut',
				height: 300,
			},
			labels: ['Milk', 'Yogurt', 'Cheese', 'Butter', 'Ice Cream'],
			colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
			legend: {
				position: 'bottom',
			},
		},
	};

	return (
		<PageWrapper>
			<Head>
				<title>Sales & Marketing Dashboard - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='TrendingUp' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Sales & Marketing Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						tag='a'
						to='/sales/orders'>
						New Sales Order
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* KPI Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CurrencyRupee' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>₹6.8L</div>
										<div className='text-muted'>Monthly Revenue</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+15% from last month
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='ShoppingCart' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>342</div>
										<div className='text-muted'>Orders This Month</div>
										<div className='small text-info'>
											<Icon icon='Add' className='me-1' />
											28 new today
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>156</div>
										<div className='text-muted'>Active Customers</div>
										<div className='small text-success'>
											<Icon icon='PersonAdd' className='me-1' />
											12 new this week
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Percent' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>18.5%</div>
										<div className='text-muted'>Profit Margin</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+2.3% improvement
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Charts Row */}
				<div className='row mb-4'>
					<div className='col-lg-8'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Sales Revenue Trend</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={salesTrendData.series}
									options={salesTrendData.options}
									type='area'
									height={350}
								/>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-4'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Product Sales Distribution</CardTitle>
							</CardHeader>
							<CardBody>
								<Chart
									series={productSalesData.series}
									options={productSalesData.options}
									type='donut'
									height={300}
								/>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions and Recent Activities */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Quick Actions</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales/orders'>
											<Icon icon='ShoppingCart' className='me-2' />
											Sales Orders
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales/customers'>
											<Icon icon='People' className='me-2' />
											Customers
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales/products'>
											<Icon icon='Inventory' className='me-2' />
											Products
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales/reports'>
											<Icon icon='Analytics' className='me-2' />
											Sales Reports
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='secondary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales/pricing'>
											<Icon icon='LocalOffer' className='me-2' />
											Pricing
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='dark'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/sales/campaigns'>
											<Icon icon='Campaign' className='me-2' />
											Campaigns
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Sales Activities</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Large order received</h6>
											<p className='timeline-text'>₹85,000 order from Retail Chain ABC</p>
											<small className='text-muted'>1 hour ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-primary'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>New customer registered</h6>
											<p className='timeline-text'>XYZ Supermarket joined as distributor</p>
											<small className='text-muted'>3 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Price update applied</h6>
											<p className='timeline-text'>Seasonal pricing for premium products</p>
											<small className='text-muted'>5 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Marketing campaign launched</h6>
											<p className='timeline-text'>Winter special promotion started</p>
											<small className='text-muted'>1 day ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default SalesDashboard;

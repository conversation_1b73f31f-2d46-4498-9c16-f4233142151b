import React, { useState } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Modal, { ModalBody, ModalFooter, ModalHeader, ModalTitle } from '../../components/bootstrap/Modal';
import FormGroup from '../../components/bootstrap/forms/FormGroup';
import Input from '../../components/bootstrap/forms/Input';
import Label from '../../components/bootstrap/forms/Label';
import Select from '../../components/bootstrap/forms/Select';
import Option from '../../components/bootstrap/Option';

const SalesOrdersPage: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);
	const [isModalOpen, setIsModalOpen] = useState(false);

	// Sample sales orders data
	const salesOrders = [
		{
			id: 'SO-2024-001',
			customer: 'Retail Chain ABC',
			date: '2024-01-15',
			amount: 85000,
			status: 'Confirmed',
			items: 12,
			deliveryDate: '2024-01-18',
			paymentStatus: 'Paid',
		},
		{
			id: 'SO-2024-002',
			customer: 'XYZ Supermarket',
			date: '2024-01-14',
			amount: 42000,
			status: 'Processing',
			items: 8,
			deliveryDate: '2024-01-17',
			paymentStatus: 'Pending',
		},
		{
			id: 'SO-2024-003',
			customer: 'Local Distributor Ltd',
			date: '2024-01-13',
			amount: 28000,
			status: 'Shipped',
			items: 6,
			deliveryDate: '2024-01-16',
			paymentStatus: 'Paid',
		},
		{
			id: 'SO-2024-004',
			customer: 'Corner Store Network',
			date: '2024-01-12',
			amount: 15000,
			status: 'Delivered',
			items: 4,
			deliveryDate: '2024-01-15',
			paymentStatus: 'Paid',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Confirmed': return 'info';
			case 'Processing': return 'warning';
			case 'Shipped': return 'primary';
			case 'Delivered': return 'success';
			case 'Cancelled': return 'danger';
			default: return 'secondary';
		}
	};

	const getPaymentStatusColor = (status: string) => {
		switch (status) {
			case 'Paid': return 'success';
			case 'Pending': return 'warning';
			case 'Overdue': return 'danger';
			default: return 'secondary';
		}
	};

	return (
		<PageWrapper>
			<Head>
				<title>Sales Orders - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='ShoppingCart' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Sales Orders Management</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						onClick={() => setIsModalOpen(true)}>
						Create Sales Order
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				<div className='row'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='ShoppingCart' /> Sales Orders
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Order Number</th>
												<th>Customer</th>
												<th>Date</th>
												<th>Amount</th>
												<th>Items</th>
												<th>Delivery Date</th>
												<th>Status</th>
												<th>Payment</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{salesOrders.map((order) => (
												<tr key={order.id}>
													<td>
														<div className='fw-bold text-primary'>{order.id}</div>
													</td>
													<td>{order.customer}</td>
													<td>{new Date(order.date).toLocaleDateString()}</td>
													<td>
														<span className='fw-bold'>₹{order.amount.toLocaleString()}</span>
													</td>
													<td>
														<Badge color='info' isLight>
															{order.items} items
														</Badge>
													</td>
													<td>{new Date(order.deliveryDate).toLocaleDateString()}</td>
													<td>
														<Badge color={getStatusColor(order.status)} isLight>
															{order.status}
														</Badge>
													</td>
													<td>
														<Badge color={getPaymentStatusColor(order.paymentStatus)} isLight>
															{order.paymentStatus}
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															View
														</Button>
														<Button
															color='success'
															isLight
															size='sm'
															icon='Edit'
															className='me-2'>
															Edit
														</Button>
														<Button
															color='info'
															isLight
															size='sm'
															icon='Print'>
															Invoice
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Summary Cards */}
				<div className='row mt-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Assignment' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Confirmed Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='HourglassTop' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Processing Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='LocalShipping' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Shipped Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Delivered Orders</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>

			{/* Create Sales Order Modal */}
			<Modal isOpen={isModalOpen} setIsOpen={setIsModalOpen} size='xl'>
				<ModalHeader setIsOpen={setIsModalOpen}>
					<ModalTitle>Create New Sales Order</ModalTitle>
				</ModalHeader>
				<ModalBody>
					<div className='row g-4'>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='customer'>Customer</Label>
								<Select id='customer'>
									<Option value=''>Select Customer</Option>
									<Option value='retail-abc'>Retail Chain ABC</Option>
									<Option value='xyz-super'>XYZ Supermarket</Option>
									<Option value='local-dist'>Local Distributor Ltd</Option>
									<Option value='corner-store'>Corner Store Network</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='deliveryDate'>Delivery Date</Label>
								<Input id='deliveryDate' type='date' />
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='priority'>Priority</Label>
								<Select id='priority'>
									<Option value='normal'>Normal</Option>
									<Option value='high'>High</Option>
									<Option value='urgent'>Urgent</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-md-6'>
							<FormGroup>
								<Label htmlFor='paymentTerms'>Payment Terms</Label>
								<Select id='paymentTerms'>
									<Option value='cod'>Cash on Delivery</Option>
									<Option value='net15'>Net 15 Days</Option>
									<Option value='net30'>Net 30 Days</Option>
									<Option value='advance'>Advance Payment</Option>
								</Select>
							</FormGroup>
						</div>
						<div className='col-12'>
							<FormGroup>
								<Label htmlFor='notes'>Order Notes</Label>
								<Input id='notes' placeholder='Special instructions or notes' />
							</FormGroup>
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color='secondary' onClick={() => setIsModalOpen(false)}>
						Cancel
					</Button>
					<Button color='primary' onClick={() => setIsModalOpen(false)}>
						Create Order
					</Button>
				</ModalFooter>
			</Modal>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default SalesOrdersPage;

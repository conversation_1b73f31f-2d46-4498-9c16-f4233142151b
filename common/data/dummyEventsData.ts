import dayjs from 'dayjs';
import USERS, { IUserProps } from './userDummyData';
import EVENT_STATUS, { IEventStatus } from './enumEventStatus';
import SERVICES, { IServiceProps } from './serviceDummyData';

const data: {
	id: number;
	status: IEventStatus['key'];
	date: string;
	time: number | string;
	customer: { name: string; email: string };
	assigned: IUserProps;
	service: IServiceProps;
	duration: string;
	payment: number | null;
}[] = [
	{
		id: 1,
		status: EVENT_STATUS.APPROVED,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(1, 'days').format('DD'),
		time: 1030,
		customer: { name: '<PERSON>', email: '<EMAIL>' },
		assigned: USERS.GRACE,
		service: SERVICES.ICE_SKATING,
		duration: '45min',
		payment: 15,
	},
	{
		id: 2,
		status: EVENT_STATUS.APPROVED,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(1, 'days').format('DD'),
		time: 1200,
		customer: { name: 'Diane Bower', email: '<EMAIL>' },
		assigned: USERS.JANE,
		service: SERVICES.YOGA,
		duration: '45min',
		payment: 40,
	},
	{
		id: 3,
		status: EVENT_STATUS.CANCELED,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(1, 'days').format('DD'),
		time: 1230,
		customer: { name: 'Claire Campbell', email: '<EMAIL>' },
		assigned: USERS.ELLA,
		service: SERVICES.TENNIS,
		duration: '45min',
		payment: 40,
	},
	{
		id: 4,
		status: EVENT_STATUS.REJECTED,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(1, 'days').format('DD'),
		time: 1500,
		customer: { name: 'Sue Quinn', email: '<EMAIL>' },
		assigned: USERS.RYAN,
		service: SERVICES.HANDBALL,
		duration: '45min',
		payment: 120,
	},
	{
		id: 5,
		status: EVENT_STATUS.APPROVED,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(2, 'days').format('DD'),
		time: '0930',
		customer: { name: 'Gabrielle Powell', email: '<EMAIL>' },
		assigned: USERS.ELLA,
		service: SERVICES.SNOWBOARDING,
		duration: '1h',
		payment: 45,
	},
	{
		id: 6,
		status: EVENT_STATUS.REJECTED,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(3, 'days').format('DD'),
		time: 1530,
		customer: { name: 'Emily Taylor', email: '<EMAIL>' },
		assigned: USERS.JANE,
		service: SERVICES.HANDBALL,
		duration: '45min',
		payment: null,
	},
	{
		id: 7,
		status: EVENT_STATUS.PENDING,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(3, 'days').format('DD'),
		time: 1130,
		customer: { name: 'Carolyn Morgan', email: '<EMAIL>' },
		assigned: USERS.JANE,
		service: SERVICES.CRICKET,
		duration: '30min',
		payment: null,
	},
	{
		id: 8,
		status: EVENT_STATUS.PENDING,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(4, 'days').format('DD'),
		time: 1300,
		customer: { name: 'Penelope North', email: '<EMAIL>' },
		assigned: USERS.RYAN,
		service: SERVICES.HIKING,
		duration: '1h',
		payment: null,
	},
	{
		id: 9,
		status: EVENT_STATUS.PENDING,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(4, 'days').format('DD'),
		time: 1530,
		customer: { name: 'Alexander Kelly', email: '<EMAIL>' },
		assigned: USERS.ELLA,
		service: SERVICES.YOGA,
		duration: '45min',
		payment: null,
	},
	{
		id: 10,
		status: EVENT_STATUS.PENDING,
		date: dayjs().format('YYYY') + dayjs().format('MM') + dayjs().add(4, 'days').format('DD'),
		time: 1600,
		customer: { name: 'Cameron Hodges', email: '<EMAIL>' },
		assigned: USERS.GRACE,
		service: SERVICES.KITE_SURFING,
		duration: '30min',
		payment: null,
	},
];

export default data;
